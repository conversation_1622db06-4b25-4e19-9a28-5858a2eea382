package com.Nguyen.blogplatform.service;

import com.Nguyen.blogplatform.exception.CommentDepthException;
import com.Nguyen.blogplatform.exception.NotFoundException;
import com.Nguyen.blogplatform.model.Comment;
import com.Nguyen.blogplatform.model.Post;
import com.Nguyen.blogplatform.model.User;
import com.Nguyen.blogplatform.payload.request.CommentRequest;
import com.Nguyen.blogplatform.payload.response.CommentResponse;
import com.Nguyen.blogplatform.repository.CommentRepository;
import com.Nguyen.blogplatform.repository.PostRepository;
import com.Nguyen.blogplatform.repository.UserRepository;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CommentServices {
    private static final int MAX_REPLY_DEPTH = 3;
    private static final int MAX_REPLIES_PER_LEVEL = 5;

    private final CommentRepository commentRepository;
    private final PostRepository postRepository;
    private final UserRepository userRepository;
    private final ModelMapper  modelMapper;
    private final NotificationService notificationService;


    @Transactional
    @Caching(evict = {
            @CacheEvict(value = "comments", key = "#postId"),
            @CacheEvict(value = "comments", key = "#postId + '-*'", allEntries = true),
            @CacheEvict(value = "replies", key = "#request.parentCommentId", condition = "#request.parentCommentId != null")
    })
    public CommentResponse createComment(String postId, CommentRequest request) {

        // Giữ SecurityContext để truyền vào thread phụ
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // Lấy Post bất đồng bộ
        CompletableFuture<Post> postFuture = CompletableFuture.supplyAsync(() ->
                postRepository.findById(postId)
                        .orElseThrow(() -> new NotFoundException("Post Not Found: " + postId))
        );

        // Lấy User bất đồng bộ, với SecurityContext được truyền tay
        CompletableFuture<User> userFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(SecurityContextHolder.createEmptyContext());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            return getCurrentUser();
        });

        // Chờ cả hai hoàn tất
        CompletableFuture.allOf(postFuture, userFuture).join();

        // Lấy kết quả
        Post post = postFuture.join();
        User user = userFuture.join();

        // Tạo comment
        Comment comment = new Comment();
        comment.setContent(request.getContent());
        comment.setPost(post);
        comment.setUser(user);
        comment.setDepth(0);

        // Xử lý nếu là reply
        if (request.getParentCommentId() != null) {
            processParentComment(comment, request.getParentCommentId());
        }

        // Lưu comment
        CommentResponse response = saveAndConvert(comment);

        // Gửi notification bất đồng bộ (không cần SecurityContext ở đây)
        CompletableFuture.runAsync(() -> {
            notificationService.sendCommentNotification(postId, response);
            notificationService.sendGlobalNotification(
                    "New comment from: " + response.getAuthorUsername() + " on post: " + post.getTitle());
        });

        return response;
    }


    private void processParentComment(Comment comment, String parentId) {
        Comment parent = commentRepository.findById(parentId)
                .orElseThrow(() -> new NotFoundException(" Not Found"+  parentId));

        if (parent.getDepth() >= MAX_REPLY_DEPTH) {
            throw new CommentDepthException("Maximum reply depth of " + MAX_REPLY_DEPTH + " reached");
        }

        comment.setParentComment(parent);
        comment.setDepth(parent.getDepth() + 1);

        parent.addReply(comment);
    }

    @Transactional(readOnly = true)
    @Cacheable(value = "comments", key = "#postId + '-' + #pageable.pageNumber + '-' + #pageable.pageSize")
    public Page<CommentResponse> getTopLevelComments(String postId, Pageable pageable) {
        // Create a standardized page request with consistent sorting
        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                Sort.by("createdAt").descending()
        );

        // Use the optimized repository method
        Page<Comment> comments = commentRepository.findByPostIdAndParentCommentIsNull(
                postId,
                pageRequest
        );

        // Process the comments in parallel for better performance with large result sets
        return comments.map(comment -> {
            try {
                // For very large result sets, this parallel processing can significantly improve performance
                if (comments.getTotalElements() > 20) {
                    return CompletableFuture.supplyAsync(() -> convertToResponse(comment)).join();
                } else {
                    // For smaller result sets, avoid the overhead of creating threads
                    return convertToResponse(comment);
                }
            } catch (Exception e) {
                // Log the error and return a simplified response in case of conversion errors
                // This prevents one bad comment from breaking the entire page
                CommentResponse fallback = new CommentResponse();
                fallback.setId(comment.getId());
                fallback.setContent("Error loading comment: " + e.getMessage());
                return fallback;
            }
        });
    }

    @Transactional(readOnly = true)
    @Cacheable(value = "replies", key = "#commentId + '-' + #pageable.pageNumber + '-' + #pageable.pageSize")
    public Page<CommentResponse> getReplies(String commentId, Pageable pageable) {
        // Validate the comment exists before fetching replies
        commentRepository.findById(commentId)
                .orElseThrow(() -> new NotFoundException("Comment not found: " + commentId));

        // Create a standardized page request with consistent sorting
        PageRequest pageRequest = PageRequest.of(
                pageable.getPageNumber(),
                pageable.getPageSize(),
                Sort.by("createdAt").descending()
        );

        // Use the optimized repository method
        Page<Comment> replies = commentRepository.findByParentCommentId(
                commentId,
                pageRequest
        );

        // Process the replies in parallel for better performance with large result sets
        return replies.map(reply -> {
            try {
                // For very large result sets, this parallel processing can significantly improve performance
                if (replies.getTotalElements() > 10) {
                    return CompletableFuture.supplyAsync(() -> convertToResponse(reply)).join();
                } else {
                    // For smaller result sets, avoid the overhead of creating threads
                    return convertToResponse(reply);
                }
            } catch (Exception e) {
                // Log the error and return a simplified response in case of conversion errors
                CommentResponse fallback = new CommentResponse();
                fallback.setId(reply.getId());
                fallback.setContent("Error loading reply: " + e.getMessage());
                return fallback;
            }
        });
    }

    private CommentResponse convertToResponse(Comment comment) {
        // Use direct field mapping instead of ModelMapper for better performance
        CommentResponse response = new CommentResponse();
        response.setId(comment.getId());
        response.setContent(comment.getContent());
        response.setCreatedAt(comment.getCreatedAt());
        response.setUpdatedAt(comment.getUpdatedAt());
        response.setDepth(comment.getDepth());
        response.setReplyCount(comment.getReplyCount());

        // Safely get username to avoid NPE
        if (comment.getUser() != null) {
            response.setAuthorUsername(comment.getUser().getUsername());
        }

        // Set parent comment ID if exists
        if (comment.getParentComment() != null) {
            response.setParentCommentId(comment.getParentComment().getId());
        }

        // Process replies only if needed and within depth limit
        if (comment.getDepth() < MAX_REPLY_DEPTH && comment.getReplyCount() > 0) {
            // Only load replies if they exist and we're within depth limit
            if (!comment.getReplies().isEmpty()) {
                // Use parallel stream for better performance with larger reply sets
                List<CommentResponse> replyResponses = comment.getReplies().stream()
                        .sorted(Comparator.comparing(Comment::getCreatedAt).reversed())
                        .limit(MAX_REPLIES_PER_LEVEL)
                        .parallel()
                        .map(this::convertToResponse)
                        .collect(Collectors.toList());

                response.setReplies(replyResponses);
            }

            // Set flag for pagination UI
            response.setHasMoreReplies(comment.getReplyCount() > MAX_REPLIES_PER_LEVEL);
        }

        return response;
    }

    private CommentResponse saveAndConvert(Comment comment) {
        try {
            // Ensure the comment has timestamps set
            if (comment.getCreatedAt() == null) {
                comment.prePersist();
            }

            // Save the comment to the database
            Comment saved = commentRepository.save(comment);

            // Convert to response object
            return convertToResponse(saved);
        } catch (Exception e) {
            // Log the error and rethrow to ensure transaction rollback
            throw new RuntimeException("Error saving comment: " + e.getMessage(), e);
        }
    }

    private User getCurrentUser() {
        String username = SecurityContextHolder.getContext().getAuthentication().getName();
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new NotFoundException("User not found: " + username));
    }
}
