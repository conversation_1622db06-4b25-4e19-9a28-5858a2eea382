plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management' version '1.1.4'
    id("org.openapi.generator") version "7.12.0"
}

group = 'com.Nguyen'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '21'
    targetCompatibility = '21'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'

    implementation 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    
    implementation 'com.mysql:mysql-connector-j'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.0.3'
    implementation 'jakarta.validation:jakarta.validation-api:3.1.0-M2'
    // https://mvnrepository.com/artifact/org.modelmapper/modelmapper
    implementation group: 'org.modelmapper', name: 'modelmapper', version: '3.2.2'
// https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-cache
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-cache', version: '3.4.3'
// https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-websocket
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-websocket', version: '3.4.3'
    // https://mvnrepository.com/artifact/de.huxhorn.sulky/de.huxhorn.sulky.ulid
    implementation group: 'de.huxhorn.sulky', name: 'de.huxhorn.sulky.ulid', version: '8.3.0'

    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
    
    implementation 'com.github.f4b6a3:ulid-creator:2.3.0'
    
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    // https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus
// https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus
    implementation group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.15.0-M3'
// https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-starter-actuator
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-actuator', version: '3.4.4'
}

tasks.named('test') {
    useJUnitPlatform()
}

bootJar {
    archiveBaseName = 'blog-platform'
    archiveVersion = project.version
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

