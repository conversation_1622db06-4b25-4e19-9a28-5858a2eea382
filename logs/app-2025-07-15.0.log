2025-07-15 08:44:50 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Starting BlogPlatformApplicationTests using Java 21.0.6 with PID 18336 (started by Admin in F:\Java\blog-platform)
2025-07-15 08:44:50 [Test worker] DEBUG c.N.b.BlogPlatformApplicationTests - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 08:44:50 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-07-15 08:44:53 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 08:44:54 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 268 ms. Found 11 JPA repository interfaces.
2025-07-15 08:44:59 [Test worker] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 08:44:59 [Test worker] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 08:44:59 [Test worker] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 08:45:00 [Test worker] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 08:45:00 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 08:45:02 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7a92827f
2025-07-15 08:45:02 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 08:45:02 [Test worker] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 08:45:05 [Test worker] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 08:45:07 [Test worker] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 08:45:08 [Test worker] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 08:45:11 [Test worker] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 08:45:11 [Test worker] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 08:45:15 [Test worker] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 08:45:15 [Test worker] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@554d4856, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@46cd3592, org.springframework.security.web.context.SecurityContextHolderFilter@7a0eca26, org.springframework.security.web.header.HeaderWriterFilter@2d8f32a, org.springframework.web.filter.CorsFilter@2a673ee8, org.springframework.security.web.authentication.logout.LogoutFilter@6845895c, com.Nguyen.blogplatform.security.AuthTokenFilter@795239f9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1124fd8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@477ac359, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3177221b, org.springframework.security.web.session.SessionManagementFilter@c7b6c50, org.springframework.security.web.access.ExceptionTranslationFilter@6b1293a5, org.springframework.security.web.access.intercept.AuthorizationFilter@19601dc5]
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 08:45:15 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 08:45:17 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 08:45:17 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@25832704]]
2025-07-15 08:45:17 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 08:45:17 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Started BlogPlatformApplicationTests in 27.439 seconds (process running for 32.695)
2025-07-15 08:45:18 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-07-15 08:45:18 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@25832704]]
2025-07-15 08:45:18 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-07-15 08:45:18 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 08:45:18 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-15 08:45:19 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-15 09:06:42 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 5384 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 09:06:42 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 09:06:42 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 09:06:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 09:06:46 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103 ms. Found 11 JPA repository interfaces.
2025-07-15 09:06:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 09:06:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 09:06:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 09:06:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 09:06:48 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5617 ms
2025-07-15 09:06:49 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 09:06:49 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 09:06:49 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 09:06:50 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 09:06:50 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 09:06:51 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@141229f6
2025-07-15 09:06:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 09:06:51 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 09:06:53 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 09:06:54 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 09:06:54 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 09:06:55 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 09:06:57 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 09:06:57 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 09:06:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 09:06:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b0eb43f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@19106753, org.springframework.security.web.context.SecurityContextHolderFilter@2ef116e8, org.springframework.security.web.header.HeaderWriterFilter@6b00eaae, org.springframework.web.filter.CorsFilter@a2cf1b6, org.springframework.security.web.authentication.logout.LogoutFilter@46775310, com.Nguyen.blogplatform.security.AuthTokenFilter@74ee07e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@47b7883c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@781ce680, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c444506, org.springframework.security.web.session.SessionManagementFilter@43af513c, org.springframework.security.web.access.ExceptionTranslationFilter@4cf08d08, org.springframework.security.web.access.intercept.AuthorizationFilter@4e71c196]
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 09:06:59 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 09:06:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 09:06:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 09:06:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@133fb509]]
2025-07-15 09:06:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 09:06:59 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 18.785 seconds (process running for 19.564)
2025-07-15 09:07:29 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 09:07:29 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 09:07:29 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-15 09:07:29 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-15 09:07:29 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 09:07:29 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-15 09:07:30 [http-nio-8888-exec-1] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-15 09:07:59 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 09:12:53 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/newsletter/subscribe
2025-07-15 09:12:53 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/newsletter/subscribe
2025-07-15 09:15:50 [http-nio-8888-exec-8] INFO  o.a.coyote.http11.Http11Processor - Error parsing HTTP request header
 Note: further occurrences of HTTP request parsing errors will be logged at DEBUG level.
java.lang.IllegalArgumentException: Invalid character found in the request target [/api/v1/saved-posts?page=0&size=10%20\ ]. The valid characters are defined in RFC 7230 and RFC 3986
	at org.apache.coyote.http11.Http11InputBuffer.parseRequestLine(Http11InputBuffer.java:482)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:264)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-15 09:16:06 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 09:16:06 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 09:16:06 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error?page=0&size=10
2025-07-15 09:16:06 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 09:16:06 [http-nio-8888-exec-10] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 09:37:59 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-15 09:44:45 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 09:44:45 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 09:44:45 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error?page=0&size=10
2025-07-15 09:44:45 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 09:44:45 [http-nio-8888-exec-5] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 09:45:24 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts
2025-07-15 09:45:24 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 09:45:24 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-15 09:45:24 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 09:45:24 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 09:53:39 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 09:53:39 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 09:53:39 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error?page=0&size=10
2025-07-15 09:53:39 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 09:53:39 [http-nio-8888-exec-9] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:07:59 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-15 10:18:25 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/post?page=2&size=5
2025-07-15 10:18:25 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/post?page=2&size=5
2025-07-15 10:18:55 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:18:55 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:18:55 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-15 10:18:55 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:18:55 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:19:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:19:48 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:19:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-15 10:19:48 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:19:48 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:20:15 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:20:15 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:20:15 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-15 10:20:15 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:20:15 [http-nio-8888-exec-5] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:22:16 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 25344 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 10:22:16 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 10:22:16 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 10:22:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 10:22:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 88 ms. Found 11 JPA repository interfaces.
2025-07-15 10:22:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 10:22:19 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:22:19 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 10:22:19 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:22:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2478 ms
2025-07-15 10:22:19 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 10:22:19 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 10:22:19 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 10:22:20 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 10:22:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:22:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@8c3b634
2025-07-15 10:22:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:22:20 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 10:22:22 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 10:22:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 10:22:22 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 10:22:23 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 10:22:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 10:22:24 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 10:22:25 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 10:22:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@73985c99, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@36477cf, org.springframework.security.web.context.SecurityContextHolderFilter@41718278, org.springframework.security.web.header.HeaderWriterFilter@615784a8, org.springframework.web.filter.CorsFilter@21775abc, org.springframework.security.web.authentication.logout.LogoutFilter@1b33bbbe, com.Nguyen.blogplatform.security.AuthTokenFilter@21dd405a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@414c63f1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2920028c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19d65f16, org.springframework.security.web.session.SessionManagementFilter@7da660a1, org.springframework.security.web.access.ExceptionTranslationFilter@6bde050d, org.springframework.security.web.access.intercept.AuthorizationFilter@4a595315]
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 10:22:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 10:22:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 10:22:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 10:22:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@19b019a6]]
2025-07-15 10:22:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 10:22:26 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.489 seconds (process running for 11.062)
2025-07-15 10:23:26 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 10:27:31 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:27:31 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 10:27:31 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-15 10:27:31 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.savePost(java.lang.String,com.Nguyen.blogplatform.payload.request.SavePostRequest); target is of class [com.Nguyen.blogplatform.controller.SavedPostController]
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Failed to authorize ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.savePost(java.lang.String,com.Nguyen.blogplatform.payload.request.SavePostRequest); target is of class [com.Nguyen.blogplatform.controller.SavedPostController] with authorization manager org.springframework.security.config.annotation.method.configuration.DeferringObservationAuthorizationManager@3a2397b7 and decision ExpressionAuthorizationDecision [granted=false, expressionAttribute=hasRole('USER')]
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-15 10:27:32 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:27:32 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:30:21 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 28144 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 10:30:21 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 10:30:21 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 10:30:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 10:30:22 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 93 ms. Found 11 JPA repository interfaces.
2025-07-15 10:30:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 10:30:23 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:30:23 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 10:30:23 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:30:23 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2602 ms
2025-07-15 10:30:24 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 10:30:24 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 10:30:24 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 10:30:24 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 10:30:24 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:30:25 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3d3a28b5
2025-07-15 10:30:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:30:25 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 10:30:26 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 10:30:26 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 10:30:27 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 10:30:27 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 10:30:28 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 10:30:28 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 10:30:29 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 10:30:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@13d86de8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@385dae6a, org.springframework.security.web.context.SecurityContextHolderFilter@3f471e59, org.springframework.security.web.header.HeaderWriterFilter@5d1206e0, org.springframework.web.filter.CorsFilter@4d923ffc, org.springframework.security.web.authentication.logout.LogoutFilter@38f57609, com.Nguyen.blogplatform.security.AuthTokenFilter@78854721, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b3472a0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@183c6db3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ed70448, org.springframework.security.web.session.SessionManagementFilter@2c9c7f4a, org.springframework.security.web.access.ExceptionTranslationFilter@304394d8, org.springframework.security.web.access.intercept.AuthorizationFilter@21775abc]
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 10:30:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 10:30:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 10:30:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 10:30:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@1af1aae9]]
2025-07-15 10:30:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 10:30:30 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 9.899 seconds (process running for 10.469)
2025-07-15 10:31:30 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 10:31:37 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:31:37 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 10:31:37 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-15 10:31:37 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:31:37 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/aa4ba3ad-4127-454a-8f93-89fa480122e6
2025-07-15 10:31:38 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.savePost(java.lang.String,com.Nguyen.blogplatform.payload.request.SavePostRequest); target is of class [com.Nguyen.blogplatform.controller.SavedPostController]
2025-07-15 10:31:38 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Failed to authorize ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.savePost(java.lang.String,com.Nguyen.blogplatform.payload.request.SavePostRequest); target is of class [com.Nguyen.blogplatform.controller.SavedPostController] with authorization manager org.springframework.security.config.annotation.method.configuration.DeferringObservationAuthorizationManager@16978436 and decision ExpressionAuthorizationDecision [granted=false, expressionAttribute=hasRole('USER')]
2025-07-15 10:31:38 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:31:38 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-15 10:31:38 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:31:38 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:42:03 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 9744 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 10:42:03 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 10:42:03 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 10:42:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 10:42:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 113 ms. Found 11 JPA repository interfaces.
2025-07-15 10:42:06 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 10:42:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:42:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 10:42:06 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:42:06 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3171 ms
2025-07-15 10:42:07 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 10:42:07 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 10:42:07 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 10:42:07 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 10:42:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:42:08 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e54e900
2025-07-15 10:42:08 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:42:08 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 10:42:10 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 10:42:11 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 10:42:11 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 10:42:11 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 10:42:13 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 10:42:13 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 10:42:15 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 10:42:15 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@449ca6b6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@12bc2b41, org.springframework.security.web.context.SecurityContextHolderFilter@3a6e8bc0, org.springframework.security.web.header.HeaderWriterFilter@2a92128, org.springframework.web.filter.CorsFilter@61466b5f, org.springframework.security.web.authentication.logout.LogoutFilter@385dae6a, com.Nguyen.blogplatform.security.AuthTokenFilter@31d11998, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5e5616f2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@753cc26d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6eb61a31, org.springframework.security.web.session.SessionManagementFilter@69d142f6, org.springframework.security.web.access.ExceptionTranslationFilter@414c63f1, org.springframework.security.web.access.intercept.AuthorizationFilter@402cdf60]
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 10:42:15 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 10:42:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 10:42:16 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 10:42:16 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@52fe4ef2]]
2025-07-15 10:42:16 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 10:42:16 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.977 seconds (process running for 14.772)
2025-07-15 10:42:52 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:42:52 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 10:42:52 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Authorizing method invocation ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.getSavedPosts(int,int); target is of class [com.Nguyen.blogplatform.controller.SavedPostController]
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.s.a.m.AuthorizationManagerBeforeMethodInterceptor - Failed to authorize ReflectiveMethodInvocation: public org.springframework.http.ResponseEntity com.Nguyen.blogplatform.controller.SavedPostController.getSavedPosts(int,int); target is of class [com.Nguyen.blogplatform.controller.SavedPostController] with authorization manager org.springframework.security.config.annotation.method.configuration.DeferringObservationAuthorizationManager@4221a0b3 and decision ExpressionAuthorizationDecision [granted=false, expressionAttribute=hasRole('USER')]
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AccessDeniedHandlerImpl - Responding with 403 status code
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error?page=0&size=10
2025-07-15 10:42:52 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-15 10:42:52 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-15 10:43:11 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 15748 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 10:43:11 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 10:43:11 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 10:43:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 10:43:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 86 ms. Found 11 JPA repository interfaces.
2025-07-15 10:43:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 10:43:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:43:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 10:43:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:43:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2619 ms
2025-07-15 10:43:14 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 10:43:14 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 10:43:14 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 10:43:15 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 10:43:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:43:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e54e900
2025-07-15 10:43:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:43:16 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 10:43:18 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 10:43:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 10:43:20 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 10:43:20 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 10:43:22 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 10:43:22 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 10:43:23 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 10:43:23 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7359781c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@73985c99, org.springframework.security.web.context.SecurityContextHolderFilter@22846025, org.springframework.security.web.header.HeaderWriterFilter@304394d8, org.springframework.web.filter.CorsFilter@36477cf, org.springframework.security.web.authentication.logout.LogoutFilter@753cc26d, com.Nguyen.blogplatform.security.AuthTokenFilter@4104b6a6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c634247, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3f471e59, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21775abc, org.springframework.security.web.session.SessionManagementFilter@7f446346, org.springframework.security.web.access.ExceptionTranslationFilter@43af513c, org.springframework.security.web.access.intercept.AuthorizationFilter@52357015]
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 10:43:23 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 10:43:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 10:43:24 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 10:43:24 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7fdd4b67]]
2025-07-15 10:43:24 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 10:43:24 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 14.55 seconds (process running for 15.52)
2025-07-15 10:43:26 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:43:26 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 10:43:26 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-15 10:43:26 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-15 10:43:27 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-15 10:43:27 [http-nio-8888-exec-1] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-15 10:44:24 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 10:46:36 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-15 10:46:36 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-15 10:46:37 [http-nio-8888-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-15 10:49:16 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 12212 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 10:49:16 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 10:49:16 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 10:49:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 10:49:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 151 ms. Found 11 JPA repository interfaces.
2025-07-15 10:49:20 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 10:49:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 10:49:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 10:49:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 10:49:20 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3611 ms
2025-07-15 10:49:20 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 10:49:20 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 10:49:20 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 10:49:21 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 10:49:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 10:49:21 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@48b9773b
2025-07-15 10:49:21 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 10:49:21 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 10:49:23 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 10:49:24 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 10:49:24 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 10:49:24 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 10:49:26 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 10:49:26 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 10:49:29 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 10:49:29 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@10a8bfac, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5e5616f2, org.springframework.security.web.context.SecurityContextHolderFilter@1f022bcf, org.springframework.security.web.header.HeaderWriterFilter@5739e15e, org.springframework.web.filter.CorsFilter@4e71c196, org.springframework.security.web.authentication.logout.LogoutFilter@2b3472a0, com.Nguyen.blogplatform.security.AuthTokenFilter@5b076d23, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c030d36, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@243d57d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@20bc4c09, org.springframework.security.web.session.SessionManagementFilter@396d21ec, org.springframework.security.web.access.ExceptionTranslationFilter@45188301, org.springframework.security.web.access.intercept.AuthorizationFilter@2d16e71e]
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 10:49:29 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 10:49:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 10:49:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 10:49:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7c698549]]
2025-07-15 10:49:30 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 10:49:30 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 15.177 seconds (process running for 15.79)
2025-07-15 10:50:30 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 10:55:42 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 10:55:42 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 10:55:42 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-15 10:55:42 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-15 10:55:43 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-15 10:55:43 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-15 10:56:02 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 10:56:03 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 10:58:57 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/post?page=2&size=5
2025-07-15 10:58:57 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/post?page=2&size=5
2025-07-15 10:59:54 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/f7206b8b-7b89-4a48-9896-f51a7e7fa6ad
2025-07-15 10:59:54 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/f7206b8b-7b89-4a48-9896-f51a7e7fa6ad
2025-07-15 10:59:54 [http-nio-8888-exec-7] INFO  c.N.b.service.BookmarkService - User User(id=850390bb-82ef-449f-87ae-fb47ab063379, username=test , email=<EMAIL>, password=$2a$10$jsO9yKJ2boOIfLxitayxo.sHOY3oaKaTMS/Qp4CpfBH/H0t29pEEi, avatar=null, posts=[], roles=[], resetToken=null, resetTokenExpiry=null, comments=[], ratings=[], like=[], savedPosts=[]) is saving post {}
2025-07-15 10:59:54 [http-nio-8888-exec-7] INFO  c.N.b.service.BookmarkService - User test  saved post How to fuck me?
2025-07-15 11:00:00 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/post?page=2&size=5
2025-07-15 11:00:01 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/post?page=2&size=5
2025-07-15 11:00:04 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 11:00:05 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 11:20:30 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-15 11:29:31 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 12704 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 11:29:31 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 11:29:31 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 11:29:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 11:29:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 170 ms. Found 11 JPA repository interfaces.
2025-07-15 11:29:35 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 11:29:35 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 11:29:35 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 11:29:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 11:29:35 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4235 ms
2025-07-15 11:29:36 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 11:29:36 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 11:29:36 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 11:29:36 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 11:29:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 11:29:37 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@43ca96a0
2025-07-15 11:29:37 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 11:29:37 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 11:29:41 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 11:29:41 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 11:29:42 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 11:29:42 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 11:29:44 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 11:29:45 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 11:29:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 11:29:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@17556c0a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@58bb50f9, org.springframework.security.web.context.SecurityContextHolderFilter@591e1df7, org.springframework.security.web.header.HeaderWriterFilter@a28296e, org.springframework.web.filter.CorsFilter@43eb44f4, org.springframework.security.web.authentication.logout.LogoutFilter@22846025, com.Nguyen.blogplatform.security.AuthTokenFilter@140e003e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2492f6fb, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@61e7d7f2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@69d142f6, org.springframework.security.web.session.SessionManagementFilter@223a679, org.springframework.security.web.access.ExceptionTranslationFilter@59a8891e, org.springframework.security.web.access.intercept.AuthorizationFilter@542abb34]
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 11:29:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 11:29:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 11:29:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 11:29:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4433e4e2]]
2025-07-15 11:29:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 11:29:47 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.631 seconds (process running for 18.493)
2025-07-15 11:30:47 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 11:37:47 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 11:37:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 11:37:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-15 11:37:47 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-15 11:37:47 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-15 11:37:48 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-15 12:00:47 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-15 13:14:47 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 25784 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-15 13:14:47 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-15 13:14:47 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-15 13:14:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-15 13:14:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 129 ms. Found 11 JPA repository interfaces.
2025-07-15 13:14:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-15 13:14:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-15 13:14:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-15 13:14:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-15 13:14:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3803 ms
2025-07-15 13:14:52 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-15 13:14:52 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-15 13:14:52 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-15 13:14:52 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-15 13:14:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-15 13:14:53 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@11ca8f71
2025-07-15 13:14:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-15 13:14:53 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-15 13:14:55 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-15 13:14:56 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-15 13:14:56 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-15 13:14:57 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-15 13:14:58 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-15 13:14:58 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-15 13:14:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-15 13:14:59 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@8e164f2, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@13d86de8, org.springframework.security.web.context.SecurityContextHolderFilter@e6522bd, org.springframework.security.web.header.HeaderWriterFilter@31167d7, org.springframework.web.filter.CorsFilter@385dae6a, org.springframework.security.web.authentication.logout.LogoutFilter@22c5a7fd, com.Nguyen.blogplatform.security.AuthTokenFilter@41a18b54, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2c0604af, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@48322f0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4d923ffc, org.springframework.security.web.session.SessionManagementFilter@41718278, org.springframework.security.web.access.ExceptionTranslationFilter@243d57d4, org.springframework.security.web.access.intercept.AuthorizationFilter@36477cf]
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-15 13:15:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-15 13:15:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-15 13:15:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-15 13:15:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7fdd4b67]]
2025-07-15 13:15:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-15 13:15:01 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 14.9 seconds (process running for 15.544)
2025-07-15 13:15:22 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-15 13:15:22 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-15 13:15:22 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-15 13:15:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/8a8410a6-2450-4e92-9d28-6ab63f28d96a
2025-07-15 13:15:23 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/8a8410a6-2450-4e92-9d28-6ab63f28d96a
2025-07-15 13:15:23 [http-nio-8888-exec-2] INFO  c.N.b.service.BookmarkService - User User(id=850390bb-82ef-449f-87ae-fb47ab063379, username=test , email=<EMAIL>, password=$2a$10$jsO9yKJ2boOIfLxitayxo.sHOY3oaKaTMS/Qp4CpfBH/H0t29pEEi, avatar=null, posts=[], roles=[], resetToken=null, resetTokenExpiry=null, comments=[], ratings=[], like=[], savedPosts=[com.Nguyen.blogplatform.model.Bookmark@178380df]) is saving post {}
2025-07-15 13:15:24 [http-nio-8888-exec-2] INFO  c.N.b.service.BookmarkService - User test  saved post tqẻweqtwerqwerqwe
2025-07-15 13:16:01 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-15 13:17:04 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:17:04 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:27:29 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:27:29 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:36:49 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/a34c58a8-594d-485c-9d97-cfc6f245fcfc
2025-07-15 13:36:49 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/a34c58a8-594d-485c-9d97-cfc6f245fcfc
2025-07-15 13:36:49 [http-nio-8888-exec-1] INFO  c.N.b.service.BookmarkService - User User(id=850390bb-82ef-449f-87ae-fb47ab063379, username=test , email=<EMAIL>, password=$2a$10$jsO9yKJ2boOIfLxitayxo.sHOY3oaKaTMS/Qp4CpfBH/H0t29pEEi, avatar=null, posts=[], roles=[], resetToken=null, resetTokenExpiry=null, comments=[], ratings=[], like=[], savedPosts=[com.Nguyen.blogplatform.model.Bookmark@7b4d7498, com.Nguyen.blogplatform.model.Bookmark@d420369]) is saving post {}
2025-07-15 13:37:36 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/f7206b8b-7b89-4a48-9896-f51a7e7fa6ad
2025-07-15 13:37:36 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/f7206b8b-7b89-4a48-9896-f51a7e7fa6ad
2025-07-15 13:37:36 [http-nio-8888-exec-2] INFO  c.N.b.service.BookmarkService - User User(id=850390bb-82ef-449f-87ae-fb47ab063379, username=test , email=<EMAIL>, password=$2a$10$jsO9yKJ2boOIfLxitayxo.sHOY3oaKaTMS/Qp4CpfBH/H0t29pEEi, avatar=null, posts=[], roles=[], resetToken=null, resetTokenExpiry=null, comments=[], ratings=[], like=[], savedPosts=[com.Nguyen.blogplatform.model.Bookmark@4addc6d1, com.Nguyen.blogplatform.model.Bookmark@79fbe7c4]) is saving post {}
2025-07-15 13:37:42 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:37:42 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:38:12 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/saved-posts/8a8410a6-2450-4e92-9d28-6ab63f28d96a
2025-07-15 13:38:12 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/saved-posts/8a8410a6-2450-4e92-9d28-6ab63f28d96a
2025-07-15 13:38:12 [http-nio-8888-exec-4] INFO  c.N.b.service.BookmarkService - User User(id=850390bb-82ef-449f-87ae-fb47ab063379, username=test , email=<EMAIL>, password=$2a$10$jsO9yKJ2boOIfLxitayxo.sHOY3oaKaTMS/Qp4CpfBH/H0t29pEEi, avatar=null, posts=[], roles=[], resetToken=null, resetTokenExpiry=null, comments=[], ratings=[], like=[], savedPosts=[com.Nguyen.blogplatform.model.Bookmark@f5d353, com.Nguyen.blogplatform.model.Bookmark@59ebebb5]) is saving post {}
2025-07-15 13:38:18 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:38:18 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/saved-posts?page=0&size=10
2025-07-15 13:46:01 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-15 14:16:01 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
