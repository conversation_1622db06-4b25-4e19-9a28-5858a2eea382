2025-06-22 01:06:35 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 4668 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 01:06:35 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 01:06:35 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 01:06:37 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 01:06:37 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135 ms. Found 9 JPA repository interfaces.
2025-06-22 01:06:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8888 (http)
2025-06-22 01:06:38 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 01:06:38 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 01:06:39 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 01:06:39 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3339 ms
2025-06-22 01:06:39 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 01:06:39 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 01:06:39 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 01:06:39 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 01:06:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 01:06:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3d01ea33
2025-06-22 01:06:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 01:06:41 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 01:06:42 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 01:06:42 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 01:06:43 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 01:06:44 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 01:06:45 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 01:06:46 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 01:06:49 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 01:06:49 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e063a21, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d158175, org.springframework.security.web.context.SecurityContextHolderFilter@4843fc3c, org.springframework.security.web.header.HeaderWriterFilter@63813085, org.springframework.web.filter.CorsFilter@7c09f8c5, org.springframework.security.web.authentication.logout.LogoutFilter@5ffdea7c, com.Nguyen.blogplatform.security.AuthTokenFilter@730a140b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1f32fb77, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4022a256, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cc20796, org.springframework.security.web.session.SessionManagementFilter@59aba6f2, org.springframework.security.web.access.ExceptionTranslationFilter@604125bd, org.springframework.security.web.access.intercept.AuthorizationFilter@3b829ef]
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 01:06:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 01:06:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 01:06:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 01:06:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@20110864]]
2025-06-22 01:06:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 01:06:51 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 16.808 seconds (process running for 17.434)
2025-06-22 01:07:51 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 01:08:05 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 01:08:05 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 01:08:05 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 01:08:05 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/post/0296e317-1f9b-4733-8cad-a0ddcc157bdc/like
2025-06-22 01:08:06 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/post/0296e317-1f9b-4733-8cad-a0ddcc157bdc/like
2025-06-22 01:09:01 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/post/latest?limit=6&page=0&size=5
2025-06-22 01:09:01 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/post/latest?limit=6&page=0&size=5
2025-06-22 14:07:16 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Starting BlogPlatformApplicationTests using Java 21.0.6 with PID 19748 (started by Admin in F:\Java\blog-platform)
2025-06-22 14:07:16 [Test worker] DEBUG c.N.b.BlogPlatformApplicationTests - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 14:07:16 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-06-22 14:07:18 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 14:07:18 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 212 ms. Found 9 JPA repository interfaces.
2025-06-22 14:07:20 [Test worker] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 14:07:20 [Test worker] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 14:07:21 [Test worker] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 14:07:21 [Test worker] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 14:07:21 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 14:07:22 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@66d69cd2
2025-06-22 14:07:22 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 14:07:23 [Test worker] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 14:07:25 [Test worker] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 14:07:26 [Test worker] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:07:27 [Test worker] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 14:07:30 [Test worker] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 14:07:30 [Test worker] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 14:07:33 [Test worker] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 14:07:33 [Test worker] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78e52e87, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@53b464b9, org.springframework.security.web.context.SecurityContextHolderFilter@a741f59, org.springframework.security.web.header.HeaderWriterFilter@58d103a4, org.springframework.web.filter.CorsFilter@2d7157b0, org.springframework.security.web.authentication.logout.LogoutFilter@4e85c657, com.Nguyen.blogplatform.security.AuthTokenFilter@66b1f207, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17504749, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6b5e9923, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3dbdcfa2, org.springframework.security.web.session.SessionManagementFilter@52c3f893, org.springframework.security.web.access.ExceptionTranslationFilter@7fe5dd5c, org.springframework.security.web.access.intercept.AuthorizationFilter@1b4d4949]
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 14:07:33 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 14:07:34 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 14:07:34 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2b91de7c]]
2025-06-22 14:07:34 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 14:07:34 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Started BlogPlatformApplicationTests in 19.687 seconds (process running for 22.536)
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2b91de7c]]
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-22 14:07:36 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-22 14:18:13 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 13824 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 14:18:13 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 14:18:13 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 14:18:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 14:18:15 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 97 ms. Found 9 JPA repository interfaces.
2025-06-22 14:18:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 14:18:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 14:18:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 14:18:16 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 14:18:16 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3057 ms
2025-06-22 14:18:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 14:18:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 14:18:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 14:18:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 14:18:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 14:18:18 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e4348c0
2025-06-22 14:18:18 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 14:18:18 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 14:18:19 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 14:18:21 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:18:21 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 14:18:21 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 14:18:23 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 14:18:23 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 14:18:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 14:18:24 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@14270715, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3b829ef, org.springframework.security.web.context.SecurityContextHolderFilter@59aba6f2, org.springframework.security.web.header.HeaderWriterFilter@13e4a4a0, org.springframework.web.filter.CorsFilter@38657ca8, org.springframework.security.web.authentication.logout.LogoutFilter@68212585, com.Nguyen.blogplatform.security.AuthTokenFilter@1d4ee936, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7f1bf2db, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@686ae6b3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@500039b3, org.springframework.security.web.session.SessionManagementFilter@3fa43b05, org.springframework.security.web.access.ExceptionTranslationFilter@4f8d98d4, org.springframework.security.web.access.intercept.AuthorizationFilter@55623ed5]
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 14:18:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 14:18:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 14:18:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 14:18:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6f49a165]]
2025-06-22 14:18:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 14:18:25 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.157 seconds (process running for 13.851)
2025-06-22 14:19:25 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 14:43:12 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 14:43:12 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 14:43:12 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 14:43:12 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v3
2025-06-22 14:43:12 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:12 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 14:43:13 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-06-22 14:43:13 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:13 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui
2025-06-22 14:43:22 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/index.html
2025-06-22 14:43:22 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/index.html
2025-06-22 14:43:22 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-ui.css
2025-06-22 14:43:22 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-ui.css
2025-06-22 14:43:22 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/index.css
2025-06-22 14:43:22 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-initializer.js
2025-06-22 14:43:22 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-ui-bundle.js
2025-06-22 14:43:22 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-ui-standalone-preset.js
2025-06-22 14:43:22 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/index.css
2025-06-22 14:43:22 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-initializer.js
2025-06-22 14:43:22 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-ui-standalone-preset.js
2025-06-22 14:43:22 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-ui-bundle.js
2025-06-22 14:43:22 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-ui-standalone-preset.js
2025-06-22 14:43:22 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-ui-standalone-preset.js
2025-06-22 14:43:22 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-initializer.js
2025-06-22 14:43:22 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-initializer.js
2025-06-22 14:43:22 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/favicon-16x16.png
2025-06-22 14:43:22 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/favicon-16x16.png
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api-docs/swagger-config
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:22 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 14:43:23 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/swagger-ui-bundle.js
2025-06-22 14:43:23 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:23 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/swagger-ui-bundle.js
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /swagger-ui/v3
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /swagger-ui/v3
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 14:43:28 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 14:49:25 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-22 15:19:25 [MessageBroker-2] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-06-22 21:29:16 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 10400 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 21:29:17 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 21:29:17 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 21:29:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 21:29:19 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 115 ms. Found 9 JPA repository interfaces.
2025-06-22 21:29:21 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 21:29:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 21:29:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 21:29:21 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 21:29:21 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3549 ms
2025-06-22 21:29:21 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 21:29:22 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 21:29:22 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 21:29:22 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 21:29:22 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 21:29:23 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2a8e30e3
2025-06-22 21:29:23 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 21:29:23 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 21:29:25 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 21:29:25 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 21:29:26 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 21:29:26 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 21:29:28 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 21:29:28 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 21:29:30 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 21:29:30 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2d158175, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7c09f8c5, org.springframework.security.web.context.SecurityContextHolderFilter@93370c5, org.springframework.security.web.header.HeaderWriterFilter@5e4168dd, org.springframework.web.filter.CorsFilter@1cc20796, org.springframework.security.web.authentication.logout.LogoutFilter@2dadadd9, com.Nguyen.blogplatform.security.AuthTokenFilter@117bfeb8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@d95150c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@48430066, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@131af11d, org.springframework.security.web.session.SessionManagementFilter@76982efa, org.springframework.security.web.access.ExceptionTranslationFilter@3dfc8814, org.springframework.security.web.access.intercept.AuthorizationFilter@38657ca8]
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 21:29:30 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 21:29:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 21:29:31 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 21:29:31 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3bf572e0]]
2025-06-22 21:29:31 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 21:29:31 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 16.439 seconds (process running for 17.171)
2025-06-22 21:30:31 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 21:36:47 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 21:36:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 21:36:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 21:36:47 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-22 21:36:47 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 21:36:47 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-22 21:36:48 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-22 21:39:24 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/tags
2025-06-22 21:39:24 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/tags
2025-06-22 21:39:38 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 21:39:38 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 21:39:38 [http-nio-8888-exec-10] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'multipart/form-data;boundary=--------------------------953218089898639499426610;charset=UTF-8' is not supported]
2025-06-22 21:39:38 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 21:39:38 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 21:39:38 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 21:57:54 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 21:57:54 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 21:57:54 [http-nio-8888-exec-9] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'multipart/form-data;boundary=--------------------------825930677740237368860463;charset=UTF-8' is not supported]
2025-06-22 21:57:54 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 21:57:54 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 21:57:54 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:00:00 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:00:01 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:00:31 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-22 22:00:46 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/post/latest?limit=6&page=0&size=5
2025-06-22 22:00:46 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/post/latest?limit=6&page=0&size=5
2025-06-22 22:06:21 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 22464 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:06:21 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:06:21 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:06:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:06:23 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 111 ms. Found 9 JPA repository interfaces.
2025-06-22 22:06:24 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:06:24 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:06:24 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:06:24 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:06:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3019 ms
2025-06-22 22:06:25 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:06:25 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:06:25 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:06:25 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:06:25 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:06:26 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6eadd342
2025-06-22 22:06:26 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:06:26 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:06:28 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:06:29 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:06:30 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:06:30 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:06:31 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:06:32 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:06:33 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:06:33 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3a94d716, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fbeaa03, org.springframework.security.web.context.SecurityContextHolderFilter@77d50cde, org.springframework.security.web.header.HeaderWriterFilter@5547476c, org.springframework.web.filter.CorsFilter@78e547aa, org.springframework.security.web.authentication.logout.LogoutFilter@4843fc3c, com.Nguyen.blogplatform.security.AuthTokenFilter@7fcc8d9f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f3809cf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f8d98d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@766f5049, org.springframework.security.web.session.SessionManagementFilter@861bc5f, org.springframework.security.web.access.ExceptionTranslationFilter@3bf47d1e, org.springframework.security.web.access.intercept.AuthorizationFilter@6c88026b]
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:06:33 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:06:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:06:34 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:06:34 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@52f3859a]]
2025-06-22 22:06:34 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:06:34 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.893 seconds (process running for 14.527)
2025-06-22 22:07:02 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:07:02 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:07:02 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 22:07:02 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:07:02 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:07:02 [http-nio-8888-exec-1] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'multipart/form-data;boundary=--------------------------625233929266690857245269;charset=UTF-8' is not supported]
2025-06-22 22:07:02 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:07:02 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:07:02 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:07:17 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:07:17 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:07:34 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:15:35 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:15:35 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:15:35 [http-nio-8888-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'multipart/form-data;boundary=--------------------------419248411898890740548709;charset=UTF-8' is not supported]
2025-06-22 22:15:35 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:15:35 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:15:35 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:18:23 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 16824 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:18:23 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:18:23 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:18:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:18:25 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124 ms. Found 9 JPA repository interfaces.
2025-06-22 22:18:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:18:26 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:18:26 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:18:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:18:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3188 ms
2025-06-22 22:18:27 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:18:27 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:18:27 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:18:28 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:18:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:18:28 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@d1d85d0
2025-06-22 22:18:28 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:18:28 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:18:30 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:18:31 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:18:32 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:18:32 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:18:35 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:18:35 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:18:36 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:18:37 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5ffdea7c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2dadadd9, org.springframework.security.web.context.SecurityContextHolderFilter@59f41bcf, org.springframework.security.web.header.HeaderWriterFilter@2b4bcd6e, org.springframework.web.filter.CorsFilter@2d43bedb, org.springframework.security.web.authentication.logout.LogoutFilter@38f2c6a0, com.Nguyen.blogplatform.security.AuthTokenFilter@63fe055, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@63813085, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@53bf7b33, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2ca08575, org.springframework.security.web.session.SessionManagementFilter@470dbce9, org.springframework.security.web.access.ExceptionTranslationFilter@2dfde704, org.springframework.security.web.access.intercept.AuthorizationFilter@1569e9d7]
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:18:37 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:18:38 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:18:38 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:18:38 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@12a57194]]
2025-06-22 22:18:38 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:18:38 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 15.387 seconds (process running for 16.126)
2025-06-22 22:18:38 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:18:38 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:18:38 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 22:18:38 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:18:39 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:18:39 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:18:39 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:18:39 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:18:39 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:19:38 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:22:00 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 19728 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:22:01 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:22:01 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:22:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:22:03 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 159 ms. Found 9 JPA repository interfaces.
2025-06-22 22:22:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:22:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:22:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:22:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:22:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4260 ms
2025-06-22 22:22:05 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:22:06 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:22:06 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:22:06 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:22:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:22:07 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@115a7e51
2025-06-22 22:22:07 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:22:07 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:22:10 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:22:11 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:22:12 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:22:12 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:22:14 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:22:14 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:22:16 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:22:16 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2ca413f6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@66e478c7, org.springframework.security.web.context.SecurityContextHolderFilter@2202eee0, org.springframework.security.web.header.HeaderWriterFilter@4a8afff2, org.springframework.web.filter.CorsFilter@1a35f99a, org.springframework.security.web.authentication.logout.LogoutFilter@3fbeaa03, com.Nguyen.blogplatform.security.AuthTokenFilter@3c2d4274, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3fa43b05, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@c207c10, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1c32c581, org.springframework.security.web.session.SessionManagementFilter@5a3783e4, org.springframework.security.web.access.ExceptionTranslationFilter@2d980dc3, org.springframework.security.web.access.intercept.AuthorizationFilter@449be249]
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:22:16 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:22:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:22:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:22:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@cb715b9]]
2025-06-22 22:22:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:22:17 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.57 seconds (process running for 18.433)
2025-06-22 22:23:17 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:27:02 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:27:02 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:27:02 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-22 22:27:02 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:27:03 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:27:03 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:27:03 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:27:03 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:27:03 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:27:37 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:27:37 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:27:37 [http-nio-8888-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:27:37 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:27:37 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:27:37 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:27:53 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:27:53 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:27:53 [http-nio-8888-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:27:53 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:27:53 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:27:53 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.Http403ForbiddenEntryPoint - Pre-authenticated entry point called. Rejecting access
2025-06-22 22:29:12 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 14044 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:29:12 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:29:12 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:29:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:29:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103 ms. Found 9 JPA repository interfaces.
2025-06-22 22:29:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:29:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:29:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:29:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:29:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3343 ms
2025-06-22 22:29:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:29:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:29:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:29:17 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:29:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:29:17 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31c56721
2025-06-22 22:29:17 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:29:17 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:29:20 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:29:21 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:29:22 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:29:22 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:29:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:29:25 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:29:27 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:29:27 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@b234301, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@47fb7dbb, org.springframework.security.web.context.SecurityContextHolderFilter@2b4bcd6e, org.springframework.security.web.header.HeaderWriterFilter@2956985b, org.springframework.web.filter.CorsFilter@6c88026b, org.springframework.security.web.authentication.logout.LogoutFilter@12c21282, com.Nguyen.blogplatform.security.AuthTokenFilter@3c2d4274, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2d980dc3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4374c46e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1b03d582, org.springframework.security.web.session.SessionManagementFilter@31809f2b, org.springframework.security.web.access.ExceptionTranslationFilter@262ac4cc, org.springframework.security.web.access.intercept.AuthorizationFilter@38f2c6a0]
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:29:27 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:29:29 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:29:29 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:29:29 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@38321e6f]]
2025-06-22 22:29:29 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:29:29 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.564 seconds (process running for 18.439)
2025-06-22 22:29:30 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:29:30 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:29:30 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 22:29:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:29:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:29:30 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:29:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:29:30 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:29:30 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:29:33 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:29:33 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:29:33 [http-nio-8888-exec-6] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:29:33 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:29:33 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:29:33 [http-nio-8888-exec-6] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:30:28 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:34:34 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 16944 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:34:34 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:34:34 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:34:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:34:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 146 ms. Found 9 JPA repository interfaces.
2025-06-22 22:34:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:34:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:34:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:34:38 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:34:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3569 ms
2025-06-22 22:34:38 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:34:38 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:34:38 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:34:39 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:34:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:34:40 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30a20fb3
2025-06-22 22:34:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:34:40 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:34:43 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:34:44 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:34:45 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:34:45 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:34:48 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:34:48 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:34:50 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:34:50 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3fa43b05, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5083642c, org.springframework.security.web.context.SecurityContextHolderFilter@70dd4172, org.springframework.security.web.header.HeaderWriterFilter@3432a612, org.springframework.web.filter.CorsFilter@23618fa2, org.springframework.security.web.authentication.logout.LogoutFilter@4a8afff2, com.Nguyen.blogplatform.security.AuthTokenFilter@7315264e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2a670af9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3bf47d1e, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@c207c10, org.springframework.security.web.session.SessionManagementFilter@18fb1034, org.springframework.security.web.access.ExceptionTranslationFilter@394642e5, org.springframework.security.web.access.intercept.AuthorizationFilter@2202eee0]
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:34:50 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:34:51 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:34:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:34:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@21fba6fd]]
2025-06-22 22:34:51 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:34:51 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.988 seconds (process running for 18.84)
2025-06-22 22:35:51 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:36:07 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:36:07 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:36:07 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 22:36:07 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:36:08 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:36:08 [http-nio-8888-exec-1] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:36:08 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:36:08 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:36:08 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:36:19 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:36:19 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:36:19 [http-nio-8888-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.web.multipart.MultipartException: Failed to parse multipart servlet request] with root cause
org.apache.tomcat.util.http.fileupload.impl.InvalidContentTypeException: the request doesn't contain a multipart/form-data or multipart/mixed stream, content type header is application/json
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.init(FileItemIteratorImpl.java:151)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.getMultiPartStream(FileItemIteratorImpl.java:205)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.findNextItem(FileItemIteratorImpl.java:224)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.<init>(FileItemIteratorImpl.java:142)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:252)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:276)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2784)
	at org.apache.catalina.connector.Request.getParts(Request.java:2685)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:773)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.parseRequest(StandardMultipartHttpServletRequest.java:93)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:86)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:70)
	at org.springframework.web.multipart.support.MultipartResolutionDelegate.asMultipartHttpServletRequest(MultipartResolutionDelegate.java:82)
	at org.springframework.web.multipart.support.RequestPartServletServerHttpRequest.<init>(RequestPartServletServerHttpRequest.java:68)
	at org.springframework.web.servlet.mvc.method.annotation.RequestPartMethodArgumentResolver.resolveArgument(RequestPartMethodArgumentResolver.java:140)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-22 22:36:19 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:36:19 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:36:19 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:39:55 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Starting BlogPlatformApplicationTests using Java 21.0.6 with PID 23972 (started by Admin in F:\Java\blog-platform)
2025-06-22 22:39:55 [Test worker] DEBUG c.N.b.BlogPlatformApplicationTests - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:39:55 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:39:58 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:39:58 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 252 ms. Found 9 JPA repository interfaces.
2025-06-22 22:40:00 [Test worker] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:40:00 [Test worker] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:40:01 [Test worker] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:40:01 [Test worker] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:40:01 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:40:04 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@698e1bf5
2025-06-22 22:40:04 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:40:06 [Test worker] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:40:08 [Test worker] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:40:10 [Test worker] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:40:11 [Test worker] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:40:17 [Test worker] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:40:18 [Test worker] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:40:24 [Test worker] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:40:25 [Test worker] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2968ff7a, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@63cd8f26, org.springframework.security.web.context.SecurityContextHolderFilter@3a3e0f7a, org.springframework.security.web.header.HeaderWriterFilter@44c66c7b, org.springframework.web.filter.CorsFilter@29923d3d, org.springframework.security.web.authentication.logout.LogoutFilter@3065a6ee, com.Nguyen.blogplatform.security.AuthTokenFilter@2d1df853, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@27cda5f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2631075f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1bc41486, org.springframework.security.web.session.SessionManagementFilter@549eeaa3, org.springframework.security.web.access.ExceptionTranslationFilter@afcfc63, org.springframework.security.web.access.intercept.AuthorizationFilter@32a7f41b]
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:40:25 [Test worker] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:40:27 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:40:27 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@49df0145]]
2025-06-22 22:40:27 [Test worker] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:40:27 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Started BlogPlatformApplicationTests in 32.557 seconds (process running for 36.614)
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopping...
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@49df0145]]
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Stopped.
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-22 22:40:28 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-22 22:44:42 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 11916 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:44:42 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:44:42 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:44:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:44:44 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 144 ms. Found 9 JPA repository interfaces.
2025-06-22 22:44:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:44:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:44:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:44:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:44:45 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3269 ms
2025-06-22 22:44:46 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:44:46 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:44:46 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:44:46 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:44:46 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:44:47 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e451790
2025-06-22 22:44:47 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:44:47 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:44:49 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:44:50 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:44:51 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:44:51 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:44:52 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:44:53 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:44:54 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:44:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@30f1a181, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ca50ae3, org.springframework.security.web.context.SecurityContextHolderFilter@24df8abf, org.springframework.security.web.header.HeaderWriterFilter@6880c06b, org.springframework.web.filter.CorsFilter@1ad27a73, org.springframework.security.web.authentication.logout.LogoutFilter@1a445b5, com.Nguyen.blogplatform.security.AuthTokenFilter@63fe055, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7a9794bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4e5563ef, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@63813085, org.springframework.security.web.session.SessionManagementFilter@3432a612, org.springframework.security.web.access.ExceptionTranslationFilter@4d37c839, org.springframework.security.web.access.intercept.AuthorizationFilter@604125bd]
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:44:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:44:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:44:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:44:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@40b415bb]]
2025-06-22 22:44:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:44:55 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.675 seconds (process running for 14.638)
2025-06-22 22:45:05 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:45:05 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:45:05 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 22:45:05 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:45:06 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:45:06 [http-nio-8888-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.web.multipart.MultipartException: Failed to parse multipart servlet request] with root cause
org.apache.tomcat.util.http.fileupload.impl.InvalidContentTypeException: the request doesn't contain a multipart/form-data or multipart/mixed stream, content type header is application/json
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.init(FileItemIteratorImpl.java:151)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.getMultiPartStream(FileItemIteratorImpl.java:205)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.findNextItem(FileItemIteratorImpl.java:224)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.<init>(FileItemIteratorImpl.java:142)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:252)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:276)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2784)
	at org.apache.catalina.connector.Request.getParts(Request.java:2685)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:773)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.parseRequest(StandardMultipartHttpServletRequest.java:93)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:86)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:70)
	at org.springframework.web.multipart.support.MultipartResolutionDelegate.asMultipartHttpServletRequest(MultipartResolutionDelegate.java:82)
	at org.springframework.web.multipart.support.RequestPartServletServerHttpRequest.<init>(RequestPartServletServerHttpRequest.java:68)
	at org.springframework.web.servlet.mvc.method.annotation.RequestPartMethodArgumentResolver.resolveArgument(RequestPartMethodArgumentResolver.java:140)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-22 22:45:06 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:45:06 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:45:06 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:45:26 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:45:26 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:45:26 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.multipart.support.MissingServletRequestPartException: Required part 'postData' is not present.]
2025-06-22 22:45:26 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:45:26 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:45:26 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:56:53 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 5156 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:56:53 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:56:53 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:56:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:56:55 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99 ms. Found 9 JPA repository interfaces.
2025-06-22 22:56:56 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:56:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:56:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:56:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:56:56 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3332 ms
2025-06-22 22:56:57 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:56:57 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:56:57 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:56:57 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:56:57 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:56:58 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31c56721
2025-06-22 22:56:58 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:56:58 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:57:00 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:57:00 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:57:01 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:57:01 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:57:02 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:57:03 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:57:04 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:57:04 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6c53ce34, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64ec804f, org.springframework.security.web.context.SecurityContextHolderFilter@604125bd, org.springframework.security.web.header.HeaderWriterFilter@1a445b5, org.springframework.web.filter.CorsFilter@5008c5a, org.springframework.security.web.authentication.logout.LogoutFilter@73ebf7cc, com.Nguyen.blogplatform.security.AuthTokenFilter@31aec04a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30f1a181, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63813085, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5ffdea7c, org.springframework.security.web.session.SessionManagementFilter@4f8d98d4, org.springframework.security.web.access.ExceptionTranslationFilter@418b3215, org.springframework.security.web.access.intercept.AuthorizationFilter@6cf2b638]
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:57:04 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:57:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:57:05 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:57:05 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@aa8f19d]]
2025-06-22 22:57:05 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:57:05 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 12.198 seconds (process running for 12.705)
2025-06-22 22:57:26 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:57:26 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:57:26 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 22:57:26 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:57:26 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:57:27 [http-nio-8888-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-22 22:57:27 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:57:27 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:57:27 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:57:28 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:57:28 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:57:29 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported]
2025-06-22 22:57:29 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:57:29 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:57:29 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:57:58 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author
2025-06-22 22:57:58 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author
2025-06-22 22:57:58 [http-nio-8888-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 22:57:58 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:57:58 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:57:58 [http-nio-8888-exec-4] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:58:00 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author
2025-06-22 22:58:00 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author
2025-06-22 22:58:00 [http-nio-8888-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 22:58:00 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:58:00 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:58:00 [http-nio-8888-exec-5] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:58:04 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 22:58:15 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 9164 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 22:58:15 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 22:58:15 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 22:58:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 22:58:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 161 ms. Found 9 JPA repository interfaces.
2025-06-22 22:58:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 22:58:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 22:58:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 22:58:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 22:58:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3044 ms
2025-06-22 22:58:19 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 22:58:19 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 22:58:19 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 22:58:20 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 22:58:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 22:58:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31c56721
2025-06-22 22:58:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 22:58:20 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 22:58:22 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 22:58:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 22:58:22 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 22:58:23 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 22:58:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 22:58:24 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 22:58:25 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 22:58:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6a5f05f5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@207b440f, org.springframework.security.web.context.SecurityContextHolderFilter@2f3809cf, org.springframework.security.web.header.HeaderWriterFilter@559c3710, org.springframework.web.filter.CorsFilter@72f18fbd, org.springframework.security.web.authentication.logout.LogoutFilter@1f32fb77, com.Nguyen.blogplatform.security.AuthTokenFilter@63d8590c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2094869b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@64e5d7e4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7e9074b2, org.springframework.security.web.session.SessionManagementFilter@281edf6b, org.springframework.security.web.access.ExceptionTranslationFilter@e8fabad, org.springframework.security.web.access.intercept.AuthorizationFilter@3a94d716]
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 22:58:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 22:58:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 22:58:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 22:58:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2f110efa]]
2025-06-22 22:58:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 22:58:26 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 11.268 seconds (process running for 12.097)
2025-06-22 22:58:34 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 22:58:34 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 22:58:34 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 22:58:34 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:58:34 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:58:34 [http-nio-8888-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 22:58:34 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:58:34 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:58:34 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:58:46 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:58:46 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:58:46 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 22:58:46 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:58:46 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:58:46 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:58:48 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 22:58:48 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 22:58:48 [http-nio-8888-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 22:58:48 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 22:58:48 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 22:58:48 [http-nio-8888-exec-5] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 22:59:26 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:03:38 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 14740 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:03:38 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:03:38 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:03:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:03:39 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 91 ms. Found 9 JPA repository interfaces.
2025-06-22 23:03:40 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:03:40 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:03:40 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:03:40 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:03:40 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2371 ms
2025-06-22 23:03:41 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:03:41 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:03:41 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:03:41 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:03:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:03:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e0802e0
2025-06-22 23:03:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:03:42 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:03:43 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:03:44 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:03:44 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:03:44 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:03:46 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:03:46 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:03:52 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 8512 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:03:52 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:03:52 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:03:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:03:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 9 JPA repository interfaces.
2025-06-22 23:03:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:03:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:03:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:03:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:03:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2834 ms
2025-06-22 23:03:55 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:03:55 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:03:55 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:03:55 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:03:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:03:56 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3ef396c9
2025-06-22 23:03:56 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:03:56 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:03:57 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:03:58 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:03:58 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:03:58 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:03:59 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:04:00 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:04:01 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:04:01 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3a94d716, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fbeaa03, org.springframework.security.web.context.SecurityContextHolderFilter@77d50cde, org.springframework.security.web.header.HeaderWriterFilter@5547476c, org.springframework.web.filter.CorsFilter@78e547aa, org.springframework.security.web.authentication.logout.LogoutFilter@4843fc3c, com.Nguyen.blogplatform.security.AuthTokenFilter@50572c04, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f3809cf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4f8d98d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@766f5049, org.springframework.security.web.session.SessionManagementFilter@861bc5f, org.springframework.security.web.access.ExceptionTranslationFilter@3bf47d1e, org.springframework.security.web.access.intercept.AuthorizationFilter@6c88026b]
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:04:01 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:04:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:04:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:04:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@aa8f19d]]
2025-06-22 23:04:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:04:02 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.541 seconds (process running for 11.412)
2025-06-22 23:04:43 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:04:43 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:04:43 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 23:04:43 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:04:44 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:04:44 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:04:44 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:04:44 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:04:44 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:05:01 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:05:51 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 18244 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:05:51 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:05:51 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:05:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:05:53 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85 ms. Found 9 JPA repository interfaces.
2025-06-22 23:05:54 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:05:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:05:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:05:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:05:54 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2388 ms
2025-06-22 23:05:54 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:05:54 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:05:54 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:05:55 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:05:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:05:55 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@682d9f21
2025-06-22 23:05:55 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:05:55 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:05:56 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:05:57 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:05:57 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:05:57 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:05:58 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:05:59 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:06:00 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:06:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@162304e3, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3a94d716, org.springframework.security.web.context.SecurityContextHolderFilter@281edf6b, org.springframework.security.web.header.HeaderWriterFilter@1a44d89b, org.springframework.web.filter.CorsFilter@3fbeaa03, org.springframework.security.web.authentication.logout.LogoutFilter@7f1bf2db, com.Nguyen.blogplatform.security.AuthTokenFilter@1227ea21, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@705448e7, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1d4cf8ea, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@78e547aa, org.springframework.security.web.session.SessionManagementFilter@2b4bcd6e, org.springframework.security.web.access.ExceptionTranslationFilter@536d655f, org.springframework.security.web.access.intercept.AuthorizationFilter@47fb7dbb]
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:06:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:06:01 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:06:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:06:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@69075512]]
2025-06-22 23:06:01 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:06:01 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 9.759 seconds (process running for 10.311)
2025-06-22 23:06:05 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:06:05 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:06:05 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 23:06:05 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:06:06 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:06:06 [http-nio-8888-exec-1] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:06:06 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:06:06 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:06:06 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:07:01 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:21:16 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 5072 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:21:16 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:21:16 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:21:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:21:18 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92 ms. Found 9 JPA repository interfaces.
2025-06-22 23:21:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:21:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:21:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:21:19 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:21:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2178 ms
2025-06-22 23:21:19 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:21:19 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:21:19 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:21:19 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:21:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:21:20 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31c56721
2025-06-22 23:21:20 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:21:20 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:21:21 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:21:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:21:23 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:21:23 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:21:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:21:24 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:21:25 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:21:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@761eaf72, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@237b93f5, org.springframework.security.web.context.SecurityContextHolderFilter@58fb9020, org.springframework.security.web.header.HeaderWriterFilter@39088acb, org.springframework.web.filter.CorsFilter@73ebf7cc, org.springframework.security.web.authentication.logout.LogoutFilter@acc6a69, com.Nguyen.blogplatform.security.AuthTokenFilter@63d8590c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@31809f2b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4785f176, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@440f434f, org.springframework.security.web.session.SessionManagementFilter@26664caa, org.springframework.security.web.access.ExceptionTranslationFilter@47585784, org.springframework.security.web.access.intercept.AuthorizationFilter@32f53573]
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:21:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:21:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:21:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:21:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3c148f23]]
2025-06-22 23:21:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:21:26 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.347 seconds (process running for 10.845)
2025-06-22 23:21:30 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:21:30 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:21:30 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 23:21:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:21:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:21:30 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:21:30 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:21:30 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:21:30 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:22:26 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:26:40 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:26:40 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:26:40 [http-nio-8888-exec-5] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:26:40 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:26:40 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:26:40 [http-nio-8888-exec-5] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:26:47 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:26:47 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:26:47 [http-nio-8888-exec-6] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.web.multipart.MultipartException: Failed to parse multipart servlet request] with root cause
org.apache.tomcat.util.http.fileupload.impl.InvalidContentTypeException: the request doesn't contain a multipart/form-data or multipart/mixed stream, content type header is application/json
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.init(FileItemIteratorImpl.java:151)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.getMultiPartStream(FileItemIteratorImpl.java:205)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.findNextItem(FileItemIteratorImpl.java:224)
	at org.apache.tomcat.util.http.fileupload.impl.FileItemIteratorImpl.<init>(FileItemIteratorImpl.java:142)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.getItemIterator(FileUploadBase.java:252)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:276)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2784)
	at org.apache.catalina.connector.Request.getParts(Request.java:2685)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:773)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at jakarta.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:315)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.parseRequest(StandardMultipartHttpServletRequest.java:93)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:86)
	at org.springframework.web.multipart.support.StandardMultipartHttpServletRequest.<init>(StandardMultipartHttpServletRequest.java:70)
	at org.springframework.web.multipart.support.MultipartResolutionDelegate.asMultipartHttpServletRequest(MultipartResolutionDelegate.java:82)
	at org.springframework.web.multipart.support.RequestPartServletServerHttpRequest.<init>(RequestPartServletServerHttpRequest.java:68)
	at org.springframework.web.servlet.mvc.method.annotation.RequestPartMethodArgumentResolver.resolveArgument(RequestPartMethodArgumentResolver.java:140)
	at org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:122)
	at org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:224)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:178)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-22 23:26:47 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:26:47 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:26:47 [http-nio-8888-exec-6] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:37:38 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:37:38 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:37:38 [http-nio-8888-exec-8] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:37:38 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:37:38 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:37:38 [http-nio-8888-exec-8] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:40:34 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 15152 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:40:34 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:40:34 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:40:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:40:36 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 148 ms. Found 9 JPA repository interfaces.
2025-06-22 23:40:37 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:40:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:40:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:40:38 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:40:38 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3629 ms
2025-06-22 23:40:38 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:40:38 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:40:38 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:40:39 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:40:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:40:39 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@d1d85d0
2025-06-22 23:40:39 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:40:40 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:40:42 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:40:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:40:44 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:40:44 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:40:46 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:40:46 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:40:48 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:40:48 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@78e547aa, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@766f5049, org.springframework.security.web.context.SecurityContextHolderFilter@2eef2fd6, org.springframework.security.web.header.HeaderWriterFilter@6b3eb57, org.springframework.web.filter.CorsFilter@6a5f05f5, org.springframework.security.web.authentication.logout.LogoutFilter@686ae6b3, com.Nguyen.blogplatform.security.AuthTokenFilter@63fe055, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1d4cf8ea, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3dfc8814, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@207b440f, org.springframework.security.web.session.SessionManagementFilter@584d9b19, org.springframework.security.web.access.ExceptionTranslationFilter@2ef4e7f3, org.springframework.security.web.access.intercept.AuthorizationFilter@aa9b6a8]
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:40:48 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:40:49 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:40:49 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:40:49 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6fe8e276]]
2025-06-22 23:40:49 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:40:49 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 16.862 seconds (process running for 17.986)
2025-06-22 23:41:48 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:41:48 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:41:48 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 23:41:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:41:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:41:48 [http-nio-8888-exec-1] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:41:48 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:41:48 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:41:48 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:41:49 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:42:03 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 15432 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:42:03 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:42:03 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:42:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:42:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 215 ms. Found 9 JPA repository interfaces.
2025-06-22 23:42:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:42:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:42:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:42:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:42:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4344 ms
2025-06-22 23:42:08 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:42:08 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:42:08 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:42:09 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:42:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:42:09 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4cd51e4e
2025-06-22 23:42:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:42:10 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:42:12 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:42:13 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:42:14 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:42:14 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:42:16 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:42:17 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:42:18 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:42:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@47fb7dbb, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6c88026b, org.springframework.security.web.context.SecurityContextHolderFilter@861bc5f, org.springframework.security.web.header.HeaderWriterFilter@2ef4e7f3, org.springframework.web.filter.CorsFilter@1b03d582, org.springframework.security.web.authentication.logout.LogoutFilter@59aba6f2, com.Nguyen.blogplatform.security.AuthTokenFilter@144caca6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@281edf6b, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2eef2fd6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@aa9b6a8, org.springframework.security.web.session.SessionManagementFilter@43a34f02, org.springframework.security.web.access.ExceptionTranslationFilter@4d089ef7, org.springframework.security.web.access.intercept.AuthorizationFilter@148b13e5]
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:42:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:42:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:42:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:42:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@4111c517]]
2025-06-22 23:42:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:42:19 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.575 seconds (process running for 18.343)
2025-06-22 23:42:25 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:42:25 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:42:25 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-22 23:42:25 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:42:25 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:42:25 [http-nio-8888-exec-2] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:42:25 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:42:25 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:42:25 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:42:28 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:42:28 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:42:28 [http-nio-8888-exec-3] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:42:28 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:42:28 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:42:28 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:43:19 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-22 23:44:56 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:44:56 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
2025-06-22 23:44:56 [http-nio-8888-exec-6] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'application/octet-stream' is not supported]
2025-06-22 23:44:56 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:44:56 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:44:56 [http-nio-8888-exec-6] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:45:44 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/memes/upload
2025-06-22 23:45:44 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/memes/upload
2025-06-22 23:45:45 [http-nio-8888-exec-7] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Error: 1062, SQLState: 23000
2025-06-22 23:45:45 [http-nio-8888-exec-7] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - Duplicate entry 'meme-3' for key 'meme.UK_bh78ay7u52uib95m5jmk4oxx'
2025-06-22 23:45:45 [http-nio-8888-exec-7] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: org.springframework.dao.DataIntegrityViolationException: could not execute statement [Duplicate entry 'meme-3' for key 'meme.UK_bh78ay7u52uib95m5jmk4oxx'] [insert into meme (description,meme_url,name,slug,id) values (?,?,?,?,?)]; SQL [insert into meme (description,meme_url,name,slug,id) values (?,?,?,?,?)]; constraint [meme.UK_bh78ay7u52uib95m5jmk4oxx]] with root cause
java.sql.SQLIntegrityConstraintViolationException: Duplicate entry 'meme-3' for key 'meme.UK_bh78ay7u52uib95m5jmk4oxx'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:118)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1054)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1003)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1312)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:988)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:194)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.performNonBatchedMutation(AbstractMutationExecutor.java:108)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorSingleNonBatched.performNonBatchedOperations(MutationExecutorSingleNonBatched.java:40)
	at org.hibernate.engine.jdbc.mutation.internal.AbstractMutationExecutor.execute(AbstractMutationExecutor.java:52)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:175)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:113)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2873)
	at org.hibernate.action.internal.EntityInsertAction.execute(EntityInsertAction.java:104)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:632)
	at org.hibernate.engine.spi.ActionQueue.executeActions(ActionQueue.java:499)
	at org.hibernate.event.internal.AbstractFlushingEventListener.performExecutions(AbstractFlushingEventListener.java:363)
	at org.hibernate.event.internal.DefaultFlushEventListener.onFlush(DefaultFlushEventListener.java:41)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.doFlush(SessionImpl.java:1403)
	at org.hibernate.internal.SessionImpl.managedFlush(SessionImpl.java:484)
	at org.hibernate.internal.SessionImpl.flushBeforeTransactionCompletion(SessionImpl.java:2319)
	at org.hibernate.internal.SessionImpl.beforeTransactionCompletion(SessionImpl.java:1976)
	at org.hibernate.engine.jdbc.internal.JdbcCoordinatorImpl.beforeTransactionCompletion(JdbcCoordinatorImpl.java:439)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl.beforeCompletionCallback(JdbcResourceLocalTransactionCoordinatorImpl.java:169)
	at org.hibernate.resource.transaction.backend.jdbc.internal.JdbcResourceLocalTransactionCoordinatorImpl$TransactionDriverControlImpl.commit(JdbcResourceLocalTransactionCoordinatorImpl.java:267)
	at org.hibernate.engine.transaction.internal.TransactionImpl.commit(TransactionImpl.java:101)
	at org.springframework.orm.jpa.JpaTransactionManager.doCommit(JpaTransactionManager.java:562)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:794)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:757)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:676)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:426)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy184.save(Unknown Source)
	at com.Nguyen.blogplatform.service.MemeServices.addMeme(MemeServices.java:53)
	at com.Nguyen.blogplatform.controller.MemeController.uploadMeme(MemeController.java:41)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-22 23:45:45 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-06-22 23:45:45 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-22 23:45:45 [http-nio-8888-exec-7] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-06-22 23:46:03 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/memes/upload
2025-06-22 23:46:03 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/memes/upload
2025-06-22 23:49:13 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 11748 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-22 23:49:13 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-22 23:49:13 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-22 23:49:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 23:49:14 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92 ms. Found 9 JPA repository interfaces.
2025-06-22 23:49:15 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-22 23:49:15 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 23:49:15 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-22 23:49:15 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 23:49:15 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2370 ms
2025-06-22 23:49:16 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 23:49:16 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-22 23:49:16 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-22 23:49:16 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-22 23:49:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-22 23:49:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e64ae1a
2025-06-22 23:49:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-22 23:49:17 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-22 23:49:18 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-22 23:49:18 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 23:49:19 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-22 23:49:19 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-22 23:49:21 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 23:49:21 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-22 23:49:22 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-22 23:49:22 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@6cf2b638, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1653cc95, org.springframework.security.web.context.SecurityContextHolderFilter@2094869b, org.springframework.security.web.header.HeaderWriterFilter@2dfde704, org.springframework.web.filter.CorsFilter@68212585, org.springframework.security.web.authentication.logout.LogoutFilter@48430066, com.Nguyen.blogplatform.security.AuthTokenFilter@123fd460, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@604125bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59f41bcf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1569e9d7, org.springframework.security.web.session.SessionManagementFilter@705448e7, org.springframework.security.web.access.ExceptionTranslationFilter@26664caa, org.springframework.security.web.access.intercept.AuthorizationFilter@6a5f05f5]
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-22 23:49:22 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-22 23:49:23 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-22 23:49:23 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-22 23:49:23 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2dacf052]]
2025-06-22 23:49:23 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-22 23:49:23 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.595 seconds (process running for 11.183)
2025-06-22 23:49:24 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 23:49:24 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 23:49:24 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 23:49:24 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/author/write
2025-06-22 23:49:24 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/author/write
