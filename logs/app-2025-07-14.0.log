2025-07-14 21:44:36 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - Starting BlogPlatformApplicationTests using Java 21.0.6 with PID 15588 (started by Admin in F:\Java\blog-platform)
2025-07-14 21:44:36 [Test worker] DEBUG c.N.b.BlogPlatformApplicationTests - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-14 21:44:36 [Test worker] INFO  c.N.b.BlogPlatformApplicationTests - No active profile set, falling back to 1 default profile: "default"
2025-07-14 21:44:39 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-14 21:44:39 [Test worker] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 210 ms. Found 11 JPA repository interfaces.
2025-07-14 21:44:42 [Test worker] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-14 21:44:42 [Test worker] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-14 21:44:42 [Test worker] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-14 21:44:43 [Test worker] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-14 21:44:43 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-14 21:44:48 [Test worker] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@177302d6
2025-07-14 21:44:48 [Test worker] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-14 21:44:49 [Test worker] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
