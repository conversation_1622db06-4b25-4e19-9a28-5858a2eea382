2025-07-16 08:50:01 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 4040 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 08:50:01 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 08:50:01 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 08:50:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 08:50:05 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 296 ms. Found 11 JPA repository interfaces.
2025-07-16 08:50:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8888 (http)
2025-07-16 08:50:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 08:50:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 08:50:08 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 08:50:08 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6215 ms
2025-07-16 08:50:08 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 08:50:08 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 08:50:09 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 08:50:09 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 08:50:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 08:50:11 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2b06681c
2025-07-16 08:50:11 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 08:50:12 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 08:50:15 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 08:50:17 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 08:50:18 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 08:50:18 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 08:50:22 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 08:50:22 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 08:50:23 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 08:50:24 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@552cd6ad, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1062f767, org.springframework.security.web.context.SecurityContextHolderFilter@385dae6a, org.springframework.security.web.header.HeaderWriterFilter@7ef7f6d5, org.springframework.web.filter.CorsFilter@6d9020e6, org.springframework.security.web.authentication.logout.LogoutFilter@46710018, com.Nguyen.blogplatform.security.AuthTokenFilter@41fbe8c0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4bcb5415, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7be9e5fb, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4a3d0660, org.springframework.security.web.session.SessionManagementFilter@21775abc, org.springframework.security.web.access.ExceptionTranslationFilter@c444506, org.springframework.security.web.access.intercept.AuthorizationFilter@48c15d35]
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 08:50:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 08:50:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 08:50:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 08:50:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ee29735]]
2025-07-16 08:50:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 08:50:25 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 25.205 seconds (process running for 26.248)
2025-07-16 08:51:24 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 08:56:36 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 08:56:36 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 08:56:36 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 08:56:36 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 08:56:36 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 08:56:36 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 08:56:37 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-16 08:58:09 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 9736 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 08:58:09 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 08:58:09 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 08:58:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 08:58:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 122 ms. Found 11 JPA repository interfaces.
2025-07-16 08:58:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 08:58:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 08:58:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 08:58:14 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 08:58:14 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3927 ms
2025-07-16 08:58:14 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 08:58:14 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 08:58:14 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 08:58:15 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 08:58:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 08:58:15 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@11ca8f71
2025-07-16 08:58:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 08:58:16 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 08:58:18 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 08:58:19 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 08:58:20 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 08:58:20 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 08:58:22 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 08:58:23 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 08:58:24 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 08:58:24 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7146e9a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5290b819, org.springframework.security.web.context.SecurityContextHolderFilter@542abb34, org.springframework.security.web.header.HeaderWriterFilter@22846025, org.springframework.web.filter.CorsFilter@52c86b9f, org.springframework.security.web.authentication.logout.LogoutFilter@52357015, com.Nguyen.blogplatform.security.AuthTokenFilter@41a18b54, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17556c0a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69d142f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@687d4471, org.springframework.security.web.session.SessionManagementFilter@5b7e6e2d, org.springframework.security.web.access.ExceptionTranslationFilter@d57624a, org.springframework.security.web.access.intercept.AuthorizationFilter@2ad3e7e5]
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 08:58:24 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 08:58:25 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 08:58:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 08:58:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@437b2db1]]
2025-07-16 08:58:25 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 08:58:26 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 17.721 seconds (process running for 19.09)
2025-07-16 08:58:38 [http-nio-8888-exec-6] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 08:58:38 [http-nio-8888-exec-6] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 08:58:38 [http-nio-8888-exec-6] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 08:58:38 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 08:58:39 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 08:58:39 [http-nio-8888-exec-6] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-16 08:58:39 [http-nio-8888-exec-6] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null
	at com.Nguyen.blogplatform.security.JwtUtils.generateJwtToken(JwtUtils.java:42)
	at com.Nguyen.blogplatform.security.JwtUtils.generateJwtCookie(JwtUtils.java:66)
	at com.Nguyen.blogplatform.service.AuthService.authenticateUser(AuthService.java:66)
	at com.Nguyen.blogplatform.controller.AuthController.authenticateUser(AuthController.java:38)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 08:58:39 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-16 08:58:39 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 08:58:39 [http-nio-8888-exec-6] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 08:59:25 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:00:37 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 1988 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:00:37 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:00:37 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:00:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:00:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 11 JPA repository interfaces.
2025-07-16 09:00:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:00:39 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:00:39 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:00:39 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:00:39 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2462 ms
2025-07-16 09:00:40 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:00:40 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:00:40 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:00:40 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:00:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:00:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a1beb3
2025-07-16 09:00:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:00:41 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:00:42 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:00:43 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:00:43 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:00:43 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:00:45 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:00:45 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:00:46 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:00:46 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4d923ffc, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4ed70448, org.springframework.security.web.context.SecurityContextHolderFilter@b51afb2, org.springframework.security.web.header.HeaderWriterFilter@7d0bca3d, org.springframework.web.filter.CorsFilter@1eb08757, org.springframework.security.web.authentication.logout.LogoutFilter@7bfb9224, com.Nguyen.blogplatform.security.AuthTokenFilter@ab5d5c8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@48322f0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5b7e6e2d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4bcb5415, org.springframework.security.web.session.SessionManagementFilter@c634247, org.springframework.security.web.access.ExceptionTranslationFilter@47b7883c, org.springframework.security.web.access.intercept.AuthorizationFilter@289bb421]
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:00:46 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:00:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:00:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:00:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@37aae870]]
2025-07-16 09:00:47 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:00:47 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.724 seconds (process running for 11.297)
2025-07-16 09:00:49 [http-nio-8888-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:00:49 [http-nio-8888-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:00:49 [http-nio-8888-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-16 09:00:49 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 09:00:50 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 09:00:51 [http-nio-8888-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-16 09:01:47 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:05:36 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 21584 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:05:36 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:05:36 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:05:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:05:38 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 106 ms. Found 11 JPA repository interfaces.
2025-07-16 09:05:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:05:39 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:05:39 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:05:39 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:05:39 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3071 ms
2025-07-16 09:05:40 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:05:40 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:05:40 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:05:41 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:05:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:05:41 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@417751d3
2025-07-16 09:05:41 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:05:42 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:05:44 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:05:45 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:05:45 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:05:46 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:05:47 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:05:48 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:05:49 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:05:49 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@52c86b9f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@687d4471, org.springframework.security.web.context.SecurityContextHolderFilter@726d34de, org.springframework.security.web.header.HeaderWriterFilter@2c9c7f4a, org.springframework.web.filter.CorsFilter@2e309b13, org.springframework.security.web.authentication.logout.LogoutFilter@7240599c, com.Nguyen.blogplatform.security.AuthTokenFilter@11759e4a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@43eb44f4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5766f830, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@79b7aebc, org.springframework.security.web.session.SessionManagementFilter@3dddef98, org.springframework.security.web.access.ExceptionTranslationFilter@7f446346, org.springframework.security.web.access.intercept.AuthorizationFilter@3596a9d5]
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:05:49 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:05:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:05:50 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:05:50 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39124055]]
2025-07-16 09:05:50 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:05:50 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 14.505 seconds (process running for 15.159)
2025-07-16 09:06:50 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:12:47 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:12:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:12:47 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-16 09:12:47 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:12:48 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:15:02 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 4084 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:15:02 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:15:02 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:15:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:15:04 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 108 ms. Found 11 JPA repository interfaces.
2025-07-16 09:15:05 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:15:05 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:15:05 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:15:05 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:15:05 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2739 ms
2025-07-16 09:15:05 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:15:05 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:15:05 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:15:06 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:15:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:15:06 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@38a52072
2025-07-16 09:15:06 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:15:06 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:15:08 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:15:09 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:15:09 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:15:09 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:15:11 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:15:11 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:15:12 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:15:12 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@38f57609, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@55b991e0, org.springframework.security.web.context.SecurityContextHolderFilter@97af4e9, org.springframework.security.web.header.HeaderWriterFilter@697b105e, org.springframework.web.filter.CorsFilter@7bfb9224, org.springframework.security.web.authentication.logout.LogoutFilter@6592f06, com.Nguyen.blogplatform.security.AuthTokenFilter@36d8ddc5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@5d1206e0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@75ac2594, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@740e38e9, org.springframework.security.web.session.SessionManagementFilter@2ef116e8, org.springframework.security.web.access.ExceptionTranslationFilter@48455005, org.springframework.security.web.access.intercept.AuthorizationFilter@19106753]
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:15:12 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:15:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:15:13 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:15:13 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2b360641]]
2025-07-16 09:15:13 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:15:13 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 11.261 seconds (process running for 11.883)
2025-07-16 09:15:54 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:15:54 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:15:54 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-16 09:15:54 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:15:55 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:16:16 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 3656 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:16:16 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:16:16 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:16:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:16:17 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 117 ms. Found 11 JPA repository interfaces.
2025-07-16 09:16:18 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:16:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:16:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:16:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:16:18 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2465 ms
2025-07-16 09:16:19 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:16:19 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:16:19 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:16:19 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:16:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:16:19 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1e54e900
2025-07-16 09:16:19 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:16:20 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:16:21 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:16:22 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:16:22 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:16:22 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:16:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:16:24 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:16:25 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:16:25 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@13d86de8, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@385dae6a, org.springframework.security.web.context.SecurityContextHolderFilter@3f471e59, org.springframework.security.web.header.HeaderWriterFilter@5d1206e0, org.springframework.web.filter.CorsFilter@4d923ffc, org.springframework.security.web.authentication.logout.LogoutFilter@38f57609, com.Nguyen.blogplatform.security.AuthTokenFilter@6ee0ef34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2b3472a0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@183c6db3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ed70448, org.springframework.security.web.session.SessionManagementFilter@2c9c7f4a, org.springframework.security.web.access.ExceptionTranslationFilter@304394d8, org.springframework.security.web.access.intercept.AuthorizationFilter@21775abc]
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:16:25 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:16:26 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:16:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:16:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2dbc56fb]]
2025-07-16 09:16:26 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:16:26 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.415 seconds (process running for 10.931)
2025-07-16 09:16:37 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:16:37 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:16:37 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 09:16:37 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:16:37 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:17:26 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:18:11 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 10816 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:18:11 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:18:11 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:18:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:18:12 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99 ms. Found 11 JPA repository interfaces.
2025-07-16 09:18:13 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:18:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:18:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:18:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:18:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2631 ms
2025-07-16 09:18:14 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:18:14 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:18:14 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:18:15 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:18:15 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:18:16 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5740ad76
2025-07-16 09:18:16 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:18:16 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:18:19 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:18:20 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:18:21 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:18:22 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:18:24 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:18:31 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 8596 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:18:31 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:18:31 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:18:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:18:33 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 93 ms. Found 11 JPA repository interfaces.
2025-07-16 09:18:34 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:18:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:18:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:18:34 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:18:34 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2906 ms
2025-07-16 09:18:35 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:18:35 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:18:35 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:18:35 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:18:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:18:36 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bf89d3d
2025-07-16 09:18:36 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:18:36 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:18:38 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:18:39 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:18:39 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:18:40 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:18:42 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:18:43 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:18:44 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:18:44 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@10a8bfac, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5e5616f2, org.springframework.security.web.context.SecurityContextHolderFilter@1f022bcf, org.springframework.security.web.header.HeaderWriterFilter@5739e15e, org.springframework.web.filter.CorsFilter@4e71c196, org.springframework.security.web.authentication.logout.LogoutFilter@2b3472a0, com.Nguyen.blogplatform.security.AuthTokenFilter@21dd405a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@c030d36, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@243d57d4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@20bc4c09, org.springframework.security.web.session.SessionManagementFilter@396d21ec, org.springframework.security.web.access.ExceptionTranslationFilter@45188301, org.springframework.security.web.access.intercept.AuthorizationFilter@2d16e71e]
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:18:44 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:18:45 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:18:45 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:18:45 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@35bd146b]]
2025-07-16 09:18:45 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:18:45 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 15.615 seconds (process running for 16.553)
2025-07-16 09:18:51 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:18:51 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:18:51 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 09:18:51 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:18:52 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:18:52 [http-nio-8888-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null
	at com.Nguyen.blogplatform.controller.AuthController.getCurrentUser(AuthController.java:66)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 09:18:52 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-16 09:18:52 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 09:18:52 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 09:19:45 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:25:47 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 20820 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:25:47 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:25:47 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:25:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:25:49 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 147 ms. Found 11 JPA repository interfaces.
2025-07-16 09:25:50 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:25:50 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:25:50 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:25:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:25:51 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3867 ms
2025-07-16 09:25:51 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:25:51 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:25:51 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:25:52 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:25:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:25:52 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a1beb3
2025-07-16 09:25:52 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:25:52 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:25:54 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:25:54 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:25:55 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:25:55 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:25:57 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:25:57 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:25:58 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:25:58 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7146e9a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5290b819, org.springframework.security.web.context.SecurityContextHolderFilter@542abb34, org.springframework.security.web.header.HeaderWriterFilter@22846025, org.springframework.web.filter.CorsFilter@52c86b9f, org.springframework.security.web.authentication.logout.LogoutFilter@52357015, com.Nguyen.blogplatform.security.AuthTokenFilter@6ee0ef34, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17556c0a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69d142f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@687d4471, org.springframework.security.web.session.SessionManagementFilter@5b7e6e2d, org.springframework.security.web.access.ExceptionTranslationFilter@d57624a, org.springframework.security.web.access.intercept.AuthorizationFilter@2ad3e7e5]
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:25:58 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:25:59 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:25:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:25:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@2cf1c10e]]
2025-07-16 09:25:59 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:25:59 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.27 seconds (process running for 14.393)
2025-07-16 09:26:59 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:30:19 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:30:19 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:30:19 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 09:30:19 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 09:30:20 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 09:30:20 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-16 09:30:32 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:30:32 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:30:32 [http-nio-8888-exec-3] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null] with root cause
java.lang.NullPointerException: Cannot invoke "com.Nguyen.blogplatform.model.Role.getName()" because the return value of "com.Nguyen.blogplatform.service.UserDetailsImpl.getRole()" is null
	at com.Nguyen.blogplatform.controller.AuthController.getCurrentUser(AuthController.java:70)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 09:30:32 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-16 09:30:32 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 09:30:32 [http-nio-8888-exec-3] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 09:31:10 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 7588 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:31:10 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:31:10 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:31:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:31:11 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 95 ms. Found 11 JPA repository interfaces.
2025-07-16 09:31:12 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:31:12 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:31:12 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:31:12 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:31:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2605 ms
2025-07-16 09:31:13 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:31:13 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:31:13 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:31:13 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:31:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:31:14 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a1beb3
2025-07-16 09:31:14 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:31:14 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:31:15 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:31:15 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:31:16 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:31:16 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:31:17 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:31:17 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:31:19 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:31:19 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@753cc26d, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1b33bbbe, org.springframework.security.web.context.SecurityContextHolderFilter@c030d36, org.springframework.security.web.header.HeaderWriterFilter@48455005, org.springframework.web.filter.CorsFilter@5b0eb43f, org.springframework.security.web.authentication.logout.LogoutFilter@1015bd66, com.Nguyen.blogplatform.security.AuthTokenFilter@ab5d5c8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@304394d8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@97af4e9, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@19106753, org.springframework.security.web.session.SessionManagementFilter@40053c7a, org.springframework.security.web.access.ExceptionTranslationFilter@5028cec0, org.springframework.security.web.access.intercept.AuthorizationFilter@10a8bfac]
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:31:19 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:31:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:31:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:31:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@bcfe4ba]]
2025-07-16 09:31:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:31:19 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.323 seconds (process running for 10.84)
2025-07-16 09:31:33 [http-nio-8888-exec-5] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:31:33 [http-nio-8888-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:31:33 [http-nio-8888-exec-5] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-16 09:31:33 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:31:33 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:32:09 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 19352 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:32:09 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:32:09 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:32:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:32:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 11 JPA repository interfaces.
2025-07-16 09:32:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:32:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:32:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:32:12 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:32:12 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2333 ms
2025-07-16 09:32:12 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:32:12 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:32:12 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:32:12 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:32:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:32:13 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@746f8520
2025-07-16 09:32:13 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:32:13 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:32:14 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:32:14 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:32:15 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:32:15 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:32:17 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:32:17 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:32:18 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:32:18 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7146e9a4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5290b819, org.springframework.security.web.context.SecurityContextHolderFilter@542abb34, org.springframework.security.web.header.HeaderWriterFilter@22846025, org.springframework.web.filter.CorsFilter@52c86b9f, org.springframework.security.web.authentication.logout.LogoutFilter@52357015, com.Nguyen.blogplatform.security.AuthTokenFilter@420d123d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17556c0a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69d142f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@687d4471, org.springframework.security.web.session.SessionManagementFilter@5b7e6e2d, org.springframework.security.web.access.ExceptionTranslationFilter@d57624a, org.springframework.security.web.access.intercept.AuthorizationFilter@2ad3e7e5]
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:32:18 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:32:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:32:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:32:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@39124055]]
2025-07-16 09:32:19 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:32:19 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 10.52 seconds (process running for 11.035)
2025-07-16 09:33:19 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:35:33 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:35:33 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:35:33 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 09:35:33 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:35:33 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:42:08 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 24192 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:42:08 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:42:08 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:42:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:42:10 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 103 ms. Found 11 JPA repository interfaces.
2025-07-16 09:42:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:42:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:42:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:42:11 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:42:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2536 ms
2025-07-16 09:42:11 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:42:11 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:42:11 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:42:12 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:42:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:42:12 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@37a1beb3
2025-07-16 09:42:12 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:42:12 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:42:14 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:42:14 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:42:14 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:42:14 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:42:16 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:42:16 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:42:17 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:42:17 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@38e10ff0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@30df67b8, org.springframework.security.web.context.SecurityContextHolderFilter@3a3a0b3b, org.springframework.security.web.header.HeaderWriterFilter@40053c7a, org.springframework.web.filter.CorsFilter@58d8059b, org.springframework.security.web.authentication.logout.LogoutFilter@4eb3dacc, com.Nguyen.blogplatform.security.AuthTokenFilter@ab5d5c8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7f446346, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@30ebad79, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@29323270, org.springframework.security.web.session.SessionManagementFilter@5d1206e0, org.springframework.security.web.access.ExceptionTranslationFilter@dcaa310, org.springframework.security.web.access.intercept.AuthorizationFilter@7ef7f6d5]
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:42:17 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:42:17 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:42:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:42:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@3cb1a148]]
2025-07-16 09:42:17 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:42:17 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 9.948 seconds (process running for 10.46)
2025-07-16 09:43:17 [http-nio-8888-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:43:17 [http-nio-8888-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:43:17 [http-nio-8888-exec-4] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 09:43:17 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 09:43:17 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:43:17 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 09:43:18 [http-nio-8888-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-07-16 09:57:30 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 20636 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 09:57:30 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 09:57:30 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 09:57:32 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 09:57:32 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 85 ms. Found 11 JPA repository interfaces.
2025-07-16 09:57:33 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 09:57:33 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 09:57:33 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 09:57:33 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 09:57:33 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2605 ms
2025-07-16 09:57:33 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 09:57:33 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 09:57:33 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 09:57:34 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 09:57:34 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 09:57:35 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@746f8520
2025-07-16 09:57:35 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 09:57:35 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 09:57:37 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 09:57:37 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 09:57:38 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 09:57:38 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 09:57:41 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 09:57:41 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 09:57:42 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 09:57:42 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@70366fb5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@57b9ce87, org.springframework.security.web.context.SecurityContextHolderFilter@73985c99, org.springframework.security.web.header.HeaderWriterFilter@c444506, org.springframework.web.filter.CorsFilter@39e79726, org.springframework.security.web.authentication.logout.LogoutFilter@a1a7e28, com.Nguyen.blogplatform.security.AuthTokenFilter@6cd31e5f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@289bb421, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@385dae6a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48c15d35, org.springframework.security.web.session.SessionManagementFilter@52357015, org.springframework.security.web.access.ExceptionTranslationFilter@4e71c196, org.springframework.security.web.access.intercept.AuthorizationFilter@568c4909]
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 09:57:42 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 09:57:43 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 09:57:43 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 09:57:43 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@5ee29735]]
2025-07-16 09:57:43 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 09:57:43 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 13.743 seconds (process running for 14.27)
2025-07-16 09:58:26 [http-nio-8888-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 09:58:26 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 09:58:26 [http-nio-8888-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 09:58:26 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:58:26 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 09:58:43 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 09:59:32 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-07-16 09:59:32 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
2025-07-16 10:00:05 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:00:05 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:00:05 [http-nio-8888-exec-6] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-16 10:00:05 [http-nio-8888-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 10:00:05 [http-nio-8888-exec-6] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 10:00:26 [http-nio-8888-exec-2] DEBUG o.s.s.w.f.HttpStatusRequestRejectedHandler - Rejecting request due to: The request was rejected because the URL contained a potentially malicious String "//"
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:539)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:509)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 10:00:26 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing GET /error
2025-07-16 10:00:26 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 10:00:26 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 10:00:43 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/profile/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:00:43 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/profile/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:26:28 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 18072 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 10:26:28 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 10:26:28 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 10:26:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 10:26:30 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 113 ms. Found 11 JPA repository interfaces.
2025-07-16 10:26:31 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 10:26:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 10:26:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 10:26:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 10:26:31 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3358 ms
2025-07-16 10:26:32 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 10:26:32 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 10:26:32 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 10:26:32 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 10:26:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:26:33 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@306bf4c3
2025-07-16 10:26:33 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:26:33 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 10:26:35 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 10:26:35 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 10:26:36 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 10:26:36 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 10:26:37 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 10:26:37 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 10:26:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 10:26:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3b3bd5fe, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1ae042f3, org.springframework.security.web.context.SecurityContextHolderFilter@59ba219d, org.springframework.security.web.header.HeaderWriterFilter@2d16e71e, org.springframework.web.filter.CorsFilter@568e814c, org.springframework.security.web.authentication.logout.LogoutFilter@6aee292d, com.Nguyen.blogplatform.security.AuthTokenFilter@112d8c18, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@30df67b8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@191c2629, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@299d1bd6, org.springframework.security.web.session.SessionManagementFilter@55b991e0, org.springframework.security.web.access.ExceptionTranslationFilter@17556c0a, org.springframework.security.web.access.intercept.AuthorizationFilter@2ac4079f]
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 10:26:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 10:26:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 10:26:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 10:26:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@7e24d565]]
2025-07-16 10:26:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 10:26:39 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 11.726 seconds (process running for 12.272)
2025-07-16 10:27:39 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 10:31:33 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 10:31:33 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 10:31:33 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 10:31:33 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/profile/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:31:33 [http-nio-8888-exec-1] ERROR c.N.b.security.AuthTokenFilter - Cannot set user authentication: 
java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getPrincipal()" because the return value of "org.springframework.security.core.context.SecurityContext.getAuthentication()" is null
	at com.Nguyen.blogplatform.service.UserDetailsImpl.getAuthorities(UserDetailsImpl.java:65)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 10:31:33 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 10:31:33 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/profile/850390bb-82ef-449f-87ae-fb47ab063379
2025-07-16 10:31:42 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 10:31:42 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthTokenFilter - Cannot set user authentication: 
java.lang.NullPointerException: Cannot invoke "org.springframework.security.core.Authentication.getPrincipal()" because the return value of "org.springframework.security.core.context.SecurityContext.getAuthentication()" is null
	at com.Nguyen.blogplatform.service.UserDetailsImpl.getAuthorities(UserDetailsImpl.java:65)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:39)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 10:31:42 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 10:31:42 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 10:31:43 [http-nio-8888-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: java.lang.ClassCastException: class java.lang.String cannot be cast to class com.Nguyen.blogplatform.model.User (java.lang.String is in module java.base of loader 'bootstrap'; com.Nguyen.blogplatform.model.User is in unnamed module of loader 'app')] with root cause
java.lang.ClassCastException: class java.lang.String cannot be cast to class com.Nguyen.blogplatform.model.User (java.lang.String is in module java.base of loader 'bootstrap'; com.Nguyen.blogplatform.model.User is in unnamed module of loader 'app')
	at com.Nguyen.blogplatform.service.UserDetailsImpl.getAuthorities(UserDetailsImpl.java:65)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.createSuccessAuthentication(AbstractUserDetailsAuthenticationProvider.java:197)
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.createSuccessAuthentication(DaoAuthenticationProvider.java:132)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:168)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at com.Nguyen.blogplatform.service.AuthService.authenticateUser(AuthService.java:60)
	at com.Nguyen.blogplatform.controller.AuthController.authenticateUser(AuthController.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:206)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:110)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.ObservationFilterChainDecorator$FilterObservation$SimpleFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:479)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$1(ObservationFilterChainDecorator.java:340)
	at org.springframework.security.web.ObservationFilterChainDecorator.lambda$wrapSecured$0(ObservationFilterChainDecorator.java:82)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:128)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:131)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:85)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at com.Nguyen.blogplatform.security.AuthTokenFilter.doFilterInternal(AuthTokenFilter.java:46)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:227)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.wrapFilter(ObservationFilterChainDecorator.java:240)
	at org.springframework.security.web.ObservationFilterChainDecorator$AroundFilterObservation$SimpleAroundFilterObservation.lambda$wrap$0(ObservationFilterChainDecorator.java:323)
	at org.springframework.security.web.ObservationFilterChainDecorator$ObservationFilter.doFilter(ObservationFilterChainDecorator.java:224)
	at org.springframework.security.web.ObservationFilterChainDecorator$VirtualFilterChain.doFilter(ObservationFilterChainDecorator.java:137)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:195)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:352)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:268)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:175)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:150)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1736)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-16 10:31:43 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /error
2025-07-16 10:31:43 [http-nio-8888-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-07-16 10:31:43 [http-nio-8888-exec-2] ERROR c.N.b.security.AuthEntryPointJwt - Unauthorized error: Full authentication is required to access this resource
2025-07-16 10:33:24 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 7980 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 10:33:24 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 10:33:24 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 10:33:28 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 13284 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-07-16 10:33:28 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-07-16 10:33:28 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-16 10:33:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-16 10:33:29 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 100 ms. Found 11 JPA repository interfaces.
2025-07-16 10:33:30 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-07-16 10:33:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-16 10:33:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-07-16 10:33:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-16 10:33:30 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2581 ms
2025-07-16 10:33:31 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-16 10:33:31 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-07-16 10:33:31 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-16 10:33:31 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-16 10:33:31 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:33:32 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@11ca8f71
2025-07-16 10:33:32 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:33:32 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-07-16 10:33:34 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-16 10:33:34 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-16 10:33:34 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-07-16 10:33:35 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-16 10:33:36 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-16 10:33:37 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-07-16 10:33:38 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-07-16 10:33:38 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@46710018, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2ac4079f, org.springframework.security.web.context.SecurityContextHolderFilter@55b991e0, org.springframework.security.web.header.HeaderWriterFilter@43eb44f4, org.springframework.web.filter.CorsFilter@72aa6a00, org.springframework.security.web.authentication.logout.LogoutFilter@52c86b9f, com.Nguyen.blogplatform.security.AuthTokenFilter@41a18b54, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7ef7f6d5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@79611f57, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ff66a81, org.springframework.security.web.session.SessionManagementFilter@19106753, org.springframework.security.web.access.ExceptionTranslationFilter@28b992e0, org.springframework.security.web.access.intercept.AuthorizationFilter@7c9be48a]
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.JwtController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.NewsletterController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.SavedPostController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-07-16 10:33:38 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-07-16 10:33:39 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-07-16 10:33:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-07-16 10:33:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@45ee48e7]]
2025-07-16 10:33:39 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-07-16 10:33:39 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 12.071 seconds (process running for 12.991)
2025-07-16 10:34:39 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-16 10:36:41 [http-nio-8888-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-16 10:36:41 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-16 10:36:41 [http-nio-8888-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-16 10:36:41 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-07-16 10:36:41 [http-nio-8888-exec-2] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-07-16 10:36:42 [http-nio-8888-exec-2] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
