2025-06-28 22:57:47 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 17224 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-28 22:57:47 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-28 22:57:47 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-28 22:57:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-28 22:57:50 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98 ms. Found 9 JPA repository interfaces.
2025-06-28 22:57:52 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8888 (http)
2025-06-28 22:57:52 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 22:57:52 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-28 22:57:52 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 22:57:52 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4985 ms
2025-06-28 22:57:52 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-28 22:57:52 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-28 22:57:52 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-28 22:57:53 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-28 22:57:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-28 22:57:53 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4c7f2fdb
2025-06-28 22:57:53 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-28 22:57:54 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-28 22:57:55 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-28 22:57:56 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-28 22:57:57 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-28 22:57:57 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-28 22:57:58 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-28 22:57:58 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-28 22:57:59 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-28 22:58:00 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@463e413b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@28736857, org.springframework.security.web.context.SecurityContextHolderFilter@1cbe3497, org.springframework.security.web.header.HeaderWriterFilter@aa9b6a8, org.springframework.web.filter.CorsFilter@5073b0b8, org.springframework.security.web.authentication.logout.LogoutFilter@7ccf845a, com.Nguyen.blogplatform.security.AuthTokenFilter@3af17313, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@296c6cc1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2ca413f6, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@10c23a76, org.springframework.security.web.session.SessionManagementFilter@5008c5a, org.springframework.security.web.access.ExceptionTranslationFilter@148b13e5, org.springframework.security.web.access.intercept.AuthorizationFilter@16be6b36]
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-28 22:58:00 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-28 22:58:00 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-28 22:58:00 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-28 22:58:00 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@28704c3]]
2025-06-28 22:58:00 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-28 22:58:00 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 16.135 seconds (process running for 17.141)
2025-06-28 22:59:00 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-28 23:06:37 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-28 23:06:37 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-28 23:06:37 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-28 23:06:37 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:06:37 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:06:37 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:11:43 [main] INFO  c.N.b.BlogPlatformApplication - Starting BlogPlatformApplication using Java 21.0.6 with PID 57808 (F:\Java\blog-platform\build\classes\java\main started by Admin in F:\Java\blog-platform)
2025-06-28 23:11:43 [main] DEBUG c.N.b.BlogPlatformApplication - Running with Spring Boot v3.2.5, Spring v6.1.6
2025-06-28 23:11:43 [main] INFO  c.N.b.BlogPlatformApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-28 23:11:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-28 23:11:45 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 118 ms. Found 9 JPA repository interfaces.
2025-06-28 23:11:47 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8888 (http)
2025-06-28 23:11:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-28 23:11:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.20]
2025-06-28 23:11:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-28 23:11:47 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3614 ms
2025-06-28 23:11:47 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-28 23:11:47 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.4.4.Final
2025-06-28 23:11:47 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-28 23:11:48 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-28 23:11:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-28 23:11:48 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7857cb1d
2025-06-28 23:11:48 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-28 23:11:48 [main] WARN  org.hibernate.orm.deprecation - HHH90000025: MySQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-28 23:11:50 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-28 23:11:50 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-28 23:11:51 [main] DEBUG c.N.b.security.AuthTokenFilter - Filter 'authenticationJwtTokenFilter' configured for use
2025-06-28 23:11:51 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-28 23:11:53 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-28 23:11:53 [main] DEBUG o.s.w.s.s.s.WebSocketHandlerMapping - Patterns [/ws-logs/**] in 'stompWebSocketHandlerMapping'
2025-06-28 23:11:54 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 13 endpoint(s) beneath base path '/actuator'
2025-06-28 23:11:54 [main] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e063a21, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2d158175, org.springframework.security.web.context.SecurityContextHolderFilter@4843fc3c, org.springframework.security.web.header.HeaderWriterFilter@63813085, org.springframework.web.filter.CorsFilter@7c09f8c5, org.springframework.security.web.authentication.logout.LogoutFilter@5ffdea7c, com.Nguyen.blogplatform.security.AuthTokenFilter@1227ea21, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1f32fb77, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4022a256, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1cc20796, org.springframework.security.web.session.SessionManagementFilter@59aba6f2, org.springframework.security.web.access.ExceptionTranslationFilter@604125bd, org.springframework.security.web.access.intercept.AuthorizationFilter@3b829ef]
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.AuthorController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CategoryController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.CommentController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.FController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.LogController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.L.WebSocketLogController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.MemeController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.PostController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.RoleController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TagController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.TestController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UploadController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.UserController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	c.N.b.c.VideoStreamController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.b.a.w.s.e.BasicErrorController:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.a.OpenApiWebMvcResource:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerWelcomeWebMvc:
	
2025-06-28 23:11:54 [main] DEBUG o.s.w.s.m.WebSocketAnnotationMethodMessageHandler - 
	o.s.w.u.SwaggerConfigResource:
	
2025-06-28 23:11:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8888 (http) with context path ''
2025-06-28 23:11:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Starting...
2025-06-28 23:11:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [org.springframework.messaging.simp.broker.DefaultSubscriptionRegistry@6f49a165]]
2025-06-28 23:11:55 [main] INFO  o.s.m.s.b.SimpleBrokerMessageHandler - Started.
2025-06-28 23:11:55 [main] INFO  c.N.b.BlogPlatformApplication - Started BlogPlatformApplication in 12.94 seconds (process running for 13.467)
2025-06-28 23:12:02 [http-nio-8888-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-28 23:12:02 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-28 23:12:02 [http-nio-8888-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-28 23:12:02 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:12:02 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:12:02 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:12:04 [http-nio-8888-exec-1] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:12:19 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:12:19 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:12:19 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:12:19 [http-nio-8888-exec-3] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:12:32 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:12:32 [http-nio-8888-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:12:32 [http-nio-8888-exec-4] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:12:32 [http-nio-8888-exec-4] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:12:55 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-06-28 23:14:40 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:14:40 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:14:40 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:14:41 [http-nio-8888-exec-7] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:15:33 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:15:33 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:15:33 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:15:33 [http-nio-8888-exec-8] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:15:36 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:15:36 [http-nio-8888-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:15:36 [http-nio-8888-exec-9] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:15:36 [http-nio-8888-exec-9] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:16:56 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:16:56 [http-nio-8888-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:16:56 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:16:56 [http-nio-8888-exec-1] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:41:18 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:41:18 [http-nio-8888-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:41:18 [http-nio-8888-exec-3] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:41:19 [http-nio-8888-exec-3] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:42:55 [MessageBroker-1] INFO  o.s.w.s.c.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-06-28 23:45:50 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:45:50 [http-nio-8888-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:45:50 [http-nio-8888-exec-5] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:45:52 [http-nio-8888-exec-5] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:47:26 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:47:26 [http-nio-8888-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:47:26 [http-nio-8888-exec-7] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:47:26 [http-nio-8888-exec-7] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:48:18 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:48:18 [http-nio-8888-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:48:18 [http-nio-8888-exec-8] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:48:19 [http-nio-8888-exec-8] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:58:26 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Securing POST /api/v1/auth/login
2025-06-28 23:58:26 [http-nio-8888-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-28 23:58:26 [http-nio-8888-exec-10] DEBUG o.s.security.web.FilterChainProxy - Secured POST /api/v1/auth/login
2025-06-28 23:58:26 [http-nio-8888-exec-10] DEBUG o.s.s.a.d.DaoAuthenticationProvider - Authenticated user
2025-06-28 23:58:26 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Securing GET /api/v1/auth/me
2025-06-28 23:58:26 [http-nio-8888-exec-1] DEBUG o.s.security.web.FilterChainProxy - Secured GET /api/v1/auth/me
