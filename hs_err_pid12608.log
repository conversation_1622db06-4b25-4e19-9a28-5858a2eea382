#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 268435456 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3600), pid=12608, tid=4136
#
# JRE version:  (21.0+35) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (21+35-LTS-2513, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Dnet.bytebuddy.agent.attacher.dump= net.bytebuddy.agent.Attacher com.sun.tools.attach.VirtualMachine 3524 C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.13\byte-buddy-agent-1.14.13.jar false 

Host: Intel(R) Core(TM) i5-8250U CPU @ 1.60GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.2364)
Time: Sat Nov 30 16:45:41 2024 SE Asia Standard Time elapsed time: 0.016621 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000217d2b5bde0):  JavaThread "Unknown thread" [_thread_in_vm, id=4136, stack(0x0000003ccd200000,0x0000003ccd300000) (1024K)]

Stack: [0x0000003ccd200000,0x0000003ccd300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6c8d39]
V  [jvm.dll+0x854091]
V  [jvm.dll+0x85630e]
V  [jvm.dll+0x8569e3]
V  [jvm.dll+0x2809a6]
V  [jvm.dll+0x6c5425]
V  [jvm.dll+0x6b9e1a]
V  [jvm.dll+0x3555ba]
V  [jvm.dll+0x35d206]
V  [jvm.dll+0x3adf6e]
V  [jvm.dll+0x3ae218]
V  [jvm.dll+0x328fcc]
V  [jvm.dll+0x329b5b]
V  [jvm.dll+0x81c699]
V  [jvm.dll+0x3bb2a1]
V  [jvm.dll+0x805688]
V  [jvm.dll+0x44ed9e]
V  [jvm.dll+0x4506a1]
C  [jli.dll+0x52a3]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17614]
C  [ntdll.dll+0x526a1]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff887a17b48, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000217d4d8a470 WorkerThread "GC Thread#0"                     [id=9684, stack(0x0000003ccd300000,0x0000003ccd400000) (1024K)]
  0x00000217d4d9b480 ConcurrentGCThread "G1 Main Marker"            [id=10248, stack(0x0000003ccd400000,0x0000003ccd500000) (1024K)]
  0x00000217d4d9c720 WorkerThread "G1 Conc#0"                       [id=15716, stack(0x0000003ccd500000,0x0000003ccd600000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff88718c097]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff887a88648] Heap_lock - owner thread: 0x00000217d2b5bde0

Heap address: 0x0000000701800000, size: 4072 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000701800000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)

[error occurred during error reporting (printing heap information), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff887571939]
GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.008 Loaded shared library D:\Program Files\Java\jdk-21\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6a2a00000 - 0x00007ff6a2a10000 	D:\Program Files\Java\jdk-21\bin\java.exe
0x00007ff8cc790000 - 0x00007ff8cc988000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ff8cb960000 - 0x00007ff8cba1f000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ff8ca4f0000 - 0x00007ff8ca7c2000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ff8c9f40000 - 0x00007ff8ca040000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ff8c2020000 - 0x00007ff8c2039000 	D:\Program Files\Java\jdk-21\bin\jli.dll
0x00007ff8c2080000 - 0x00007ff8c209b000 	D:\Program Files\Java\jdk-21\bin\VCRUNTIME140.dll
0x00007ff8cbfd0000 - 0x00007ff8cc07e000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ff8cc190000 - 0x00007ff8cc22e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ff8cb7a0000 - 0x00007ff8cb83c000 	C:\WINDOWS\System32\sechost.dll
0x00007ff8cafc0000 - 0x00007ff8cb0e5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ff8cb0f0000 - 0x00007ff8cb291000 	C:\WINDOWS\System32\USER32.dll
0x00007ff8b54e0000 - 0x00007ff8b577a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e\COMCTL32.dll
0x00007ff8ca240000 - 0x00007ff8ca262000 	C:\WINDOWS\System32\win32u.dll
0x00007ff8caf90000 - 0x00007ff8cafbb000 	C:\WINDOWS\System32\GDI32.dll
0x00007ff8ca370000 - 0x00007ff8ca47f000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ff8ca040000 - 0x00007ff8ca0dd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ff8c3020000 - 0x00007ff8c302a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ff8cb920000 - 0x00007ff8cb952000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ff8c2010000 - 0x00007ff8c201c000 	D:\Program Files\Java\jdk-21\bin\vcruntime140_1.dll
0x00007ff88b430000 - 0x00007ff88b4be000 	D:\Program Files\Java\jdk-21\bin\msvcp140.dll
0x00007ff886e50000 - 0x00007ff887b63000 	D:\Program Files\Java\jdk-21\bin\server\jvm.dll
0x00007ff8caf20000 - 0x00007ff8caf8b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ff8c93b0000 - 0x00007ff8c93fb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ff8aea70000 - 0x00007ff8aea97000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ff8c9280000 - 0x00007ff8c9292000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ff8c8690000 - 0x00007ff8c86a2000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ff8c2000000 - 0x00007ff8c200a000 	D:\Program Files\Java\jdk-21\bin\jimage.dll
0x00007ff8c7a00000 - 0x00007ff8c7be4000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ff8b2610000 - 0x00007ff8b2645000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ff8c9e80000 - 0x00007ff8c9f02000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ff8b96b0000 - 0x00007ff8b96cf000 	D:\Program Files\Java\jdk-21\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Program Files\Java\jdk-21\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.1110_none_60b5254171f9507e;D:\Program Files\Java\jdk-21\bin\server

VM Arguments:
jvm_args: -Dnet.bytebuddy.agent.attacher.dump= 
java_command: net.bytebuddy.agent.Attacher com.sun.tools.attach.VirtualMachine 3524 C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.13\byte-buddy-agent-1.14.13.jar false 
java_class_path (initial): C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.13\byte-buddy-agent-1.14.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 268435456                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4269801472                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4269801472                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Program Files\Java\jdk-21
PATH=C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\VSCodium\bin;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;C:\Program Files\Go\bin;C:\ProgramData\ComposerSetup\bin;F:\VSCodium\bin;C:\Program Files\Neovim\bin;C:\Program Files (x86)\cloudflared\;C:\Users\<USER>\scoop\apps\nvm\current;C:\Users\<USER>\scoop\apps\nvm\current\nodejs\nodejs;C:\Users\<USER>\scoop\apps\maven\current\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;F:\php;C:\Users\<USER>\go\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin;C:\Program Files\JetBrains\PhpStorm 2024.2.1\bin;E:\Go-lang\ssh-tool;F:\PostgreSQL\17\bin;F:\PostgreSQL\17\lib;C:\Users\<USER>\.deno\bin;C:\Program Files\MySQL\MySQL Server 9.1\bin;F:\JetBrains\WebStorm 2024.3\bin;;C:\MinGW\bin;C:\Program Files\Java\jdk-20\bin;
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.2364)
OS uptime: 4 days 16:58 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for all 8 processors :
  Max Mhz: 1800, Current Mhz: 1600, Mhz Limit: 1584

Memory: 4k page, system-wide physical 16281M (2058M free)
TotalPageFile size 31465M (AvailPageFile size 172M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 60M, peak: 316M

vm_info: Java HotSpot(TM) 64-Bit Server VM (21+35-LTS-2513) for windows-amd64 JRE (21+35-LTS-2513), built on 2023-08-09T20:25:10Z by "mach5one" with MS VC++ 17.1 (VS2022)

END.
